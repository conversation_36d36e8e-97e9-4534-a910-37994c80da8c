enum PurchaseStatus {
  pending,
  purchased,
  error,
  restored,
  canceled;

  // Get the string value for logging/debugging
  String get value {
    switch (this) {
      case PurchaseStatus.pending:
        return 'pending';
      case PurchaseStatus.purchased:
        return 'purchased';
      case PurchaseStatus.error:
        return 'error';
      case PurchaseStatus.restored:
        return 'restored';
      case PurchaseStatus.canceled:
        return 'canceled';
    }
  }

  static PurchaseStatus? fromString(String value) {
    for (PurchaseStatus status in PurchaseStatus.values) {
      if (status.value == value) {
        return status;
      }
    }
    return null;
  }

  // Helper methods
  bool get isPending => this == PurchaseStatus.pending;
  bool get isPurchased => this == PurchaseStatus.purchased;
  bool get isError => this == PurchaseStatus.error;
  bool get isRestored => this == PurchaseStatus.restored;
  bool get isCanceled => this == PurchaseStatus.canceled;

  bool get isSuccessful => isPurchased || isRestored;
  bool get isFailure => isError || isCanceled;
}

enum StoreType {
  appStore,
  googlePlay,
  unknown;

  String get value {
    switch (this) {
      case StoreType.appStore:
        return 'app_store';
      case StoreType.googlePlay:
        return 'google_play';
      case StoreType.unknown:
        return 'unknown';
    }
  }

  static StoreType? fromString(String value) {
    for (StoreType type in StoreType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }

  bool get isAppStore => this == StoreType.appStore;
  bool get isGooglePlay => this == StoreType.googlePlay;
  bool get isUnknown => this == StoreType.unknown;
}

enum PaymentSystem {
  storeKit1,
  storeKit2,
  googlePlayBilling;

  String get value {
    switch (this) {
      case PaymentSystem.storeKit1:
        return 'storekit1';
      case PaymentSystem.storeKit2:
        return 'storekit2';
      case PaymentSystem.googlePlayBilling:
        return 'google_play_billing';
    }
  }

  static PaymentSystem? fromString(String value) {
    for (PaymentSystem system in PaymentSystem.values) {
      if (system.value == value) {
        return system;
      }
    }
    return null;
  }

  bool get isStoreKit1 => this == PaymentSystem.storeKit1;
  bool get isStoreKit2 => this == PaymentSystem.storeKit2;
  bool get isGooglePlayBilling => this == PaymentSystem.googlePlayBilling;
  bool get isStoreKit => isStoreKit1 || isStoreKit2;
}
