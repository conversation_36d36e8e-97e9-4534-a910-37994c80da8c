enum PurchaseStatus {
  pending,
  purchased,
  error,
  restored,
  canceled;

  static PurchaseStatus? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return PurchaseStatus.pending;
      case 'purchased':
        return PurchaseStatus.purchased;
      case 'error':
        return PurchaseStatus.error;
      case 'restored':
        return PurchaseStatus.restored;
      case 'canceled':
        return PurchaseStatus.canceled;
      default:
        return null;
    }
  }

  // Helper methods
  bool get isPending => this == PurchaseStatus.pending;
  bool get isPurchased => this == PurchaseStatus.purchased;
  bool get isError => this == PurchaseStatus.error;
  bool get isRestored => this == PurchaseStatus.restored;
  bool get isCanceled => this == PurchaseStatus.canceled;

  bool get isSuccessful => isPurchased || isRestored;
  bool get isFailure => isError || isCanceled;
}

enum StoreType {
  appStore,
  googlePlay,
  unknown;

  static StoreType? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'appstore':
        return StoreType.appStore;
      case 'googleplay':
        return StoreType.googlePlay;
      case 'unknown':
        return StoreType.unknown;
      default:
        return null;
    }
  }

  bool get isAppStore => this == StoreType.appStore;
  bool get isGooglePlay => this == StoreType.googlePlay;
  bool get isUnknown => this == StoreType.unknown;
}

enum PaymentSystem {
  storeKit1,
  storeKit2,
  googlePlayBilling;

  static PaymentSystem? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'storekit_1':
        return PaymentSystem.storeKit1;
      case 'storekit2':
        return PaymentSystem.storeKit2;
      case 'googleplaybilling':
        return PaymentSystem.googlePlayBilling;
      default:
        return null;
    }
  }

  bool get isStoreKit1 => this == PaymentSystem.storeKit1;
  bool get isStoreKit2 => this == PaymentSystem.storeKit2;
  bool get isGooglePlayBilling => this == PaymentSystem.googlePlayBilling;
  bool get isStoreKit => isStoreKit1 || isStoreKit2;
}
