import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

class SubscriptionColorMapper {
  static Color getColorFromName(String colorName) {
    switch (colorName) {
      case 'freeGradientStart':
        return AppColors.freeGradientStart;
      case 'freeGradientEnd':
        return AppColors.freeGradientEnd;
      case 'habitsGradientStart':
        return AppColors.habitsGradientStart;
      case 'habitsGradientEnd':
        return AppColors.habitsGradientEnd;
      case 'summitGradientStart':
        return AppColors.summitGradientStart;
      case 'summitGradientEnd':
        return AppColors.summitGradientEnd;
      case 'clinicalGradientStart':
        return AppColors.clinicalGradientStart;
      case 'clinicalGradientEnd':
        return AppColors.clinicalGradientEnd;
      default:
        // Fallback to default gradient colors
        return AppColors.habitGradientStart;
    }
  }

  static List<Color> getGradientColors(List<String> colorNames) {
    return colorNames.map((colorName) => getColorFromName(colorName)).toList();
  }
}
