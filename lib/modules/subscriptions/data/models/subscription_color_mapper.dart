import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

enum SubscriptionColorName {
  freeGradientStart,
  freeGradientEnd,
  habitsGradientStart,
  habitsGradientEnd,
  summitGradientStart,
  summitGradientEnd,
  clinicalGradientStart,
  clinicalGradientEnd;

  static SubscriptionColorName? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'freegradientstart':
        return SubscriptionColorName.freeGradientStart;
      case 'freegradientend':
        return SubscriptionColorName.freeGradientEnd;
      case 'habitsgradientstart':
        return SubscriptionColorName.habitsGradientStart;
      case 'habitsgradientend':
        return SubscriptionColorName.habitsGradientEnd;
      case 'summitgradientstart':
        return SubscriptionColorName.summitGradientStart;
      case 'summitgradientend':
        return SubscriptionColorName.summitGradientEnd;
      case 'clinicalgradientstart':
        return SubscriptionColorName.clinicalGradientStart;
      case 'clinicalgradientend':
        return SubscriptionColorName.clinicalGradientEnd;
      default:
        return null;
    }
  }

  Color get color {
    switch (this) {
      case SubscriptionColorName.freeGradientStart:
        return AppColors.freeGradientStart;
      case SubscriptionColorName.freeGradientEnd:
        return AppColors.freeGradientEnd;
      case SubscriptionColorName.habitsGradientStart:
        return AppColors.habitsGradientStart;
      case SubscriptionColorName.habitsGradientEnd:
        return AppColors.habitsGradientEnd;
      case SubscriptionColorName.summitGradientStart:
        return AppColors.summitGradientStart;
      case SubscriptionColorName.summitGradientEnd:
        return AppColors.summitGradientEnd;
      case SubscriptionColorName.clinicalGradientStart:
        return AppColors.clinicalGradientStart;
      case SubscriptionColorName.clinicalGradientEnd:
        return AppColors.clinicalGradientEnd;
    }
  }
}

class SubscriptionColorMapper {
  static Color getColorFromName(String colorName) {
    final colorEnum = SubscriptionColorName.fromString(colorName);
    return colorEnum?.color ?? AppColors.habitGradientStart;
  }

  static List<Color> getGradientColors(List<String> colorNames) {
    return colorNames.map((colorName) => getColorFromName(colorName)).toList();
  }
}
