import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';

enum SubscriptionIcon {
  habits,
  mylaChat,
  neurocity,
  neuroGames,
  community,
  livestreams,
  clinicalSolution;

  static SubscriptionIcon? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'habits':
        return SubscriptionIcon.habits;
      case 'mylachat':
        return SubscriptionIcon.mylaChat;
      case 'neurocity':
        return SubscriptionIcon.neurocity;
      case 'neurogames':
        return SubscriptionIcon.neuroGames;
      case 'community':
        return SubscriptionIcon.community;
      case 'livestreams':
        return SubscriptionIcon.livestreams;
      case 'clinicalsolution':
        return SubscriptionIcon.clinicalSolution;
      default:
        return null;
    }
  }

  // Helper methods
  bool get isHabits => this == SubscriptionIcon.habits;
  bool get isMylaChat => this == SubscriptionIcon.mylaChat;
  bool get isNeurocity => this == SubscriptionIcon.neurocity;
  bool get isNeuroGames => this == SubscriptionIcon.neuroGames;
  bool get isCommunity => this == SubscriptionIcon.community;
  bool get isLivestreams => this == SubscriptionIcon.livestreams;
  bool get isClinicalSolution => this == SubscriptionIcon.clinicalSolution;

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionIcon get defaultIcon => SubscriptionIcon.habits;

  String get svgPath {
    switch (this) {
      case SubscriptionIcon.habits:
        return Assets.svgs.habit.path;
      case SubscriptionIcon.mylaChat:
        return Assets.svgs.mylaChat.path;
      case SubscriptionIcon.neurocity:
        return Assets.svgs.neurocity.path;
      case SubscriptionIcon.neuroGames:
        return Assets.svgs.neuroGames.path;
      case SubscriptionIcon.community:
        return Assets.svgs.community.path;
      case SubscriptionIcon.livestreams:
        return Assets.svgs.liveStream.path;
      case SubscriptionIcon.clinicalSolution:
        return Assets.svgs.liveStream.path;
    }
  }
}

class SubscriptionIconMapper {
  static SvgPicture getIconFromName(String iconName) {
    final icon = SubscriptionIcon.fromString(iconName);
    final svgPath = icon?.svgPath ?? Assets.svgs.liveStream.path;

    return SvgPicture.asset(
      svgPath,
      width: 32,
      height: 32,
    );
  }

  static SvgPicture getIconFromEnum(SubscriptionIcon icon) {
    return SvgPicture.asset(
      icon.svgPath,
      width: 32,
      height: 32,
    );
  }
}
