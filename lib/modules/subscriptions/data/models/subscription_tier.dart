enum SubscriptionTier {
  free('free'),
  habits('habits'),
  summit('summit'),
  clinical('clinical');

  const SubscriptionTier(this.value);
  final String value;

  static SubscriptionTier? fromString(String value) {
    for (SubscriptionTier tier in SubscriptionTier.values) {
      if (tier.value == value) {
        return tier;
      }
    }
    return null;
  }

  bool get isFree => this == SubscriptionTier.free;
  bool get isPaid => this != SubscriptionTier.free;
  bool get isHabits => this == SubscriptionTier.habits;
  bool get isSummit => this == SubscriptionTier.summit;
  bool get isClinical => this == SubscriptionTier.clinical;

  // Display name formatting
  String get displayName {
    switch (this) {
      case SubscriptionTier.free:
        return 'Free Tier';
      case SubscriptionTier.habits:
        return 'Habits Tier';
      case SubscriptionTier.summit:
        return 'Summit Tier';
      case SubscriptionTier.clinical:
        return 'Clinical Tier';
    }
  }

  // Tier hierarchy (for comparison)
  int get tierLevel {
    switch (this) {
      case SubscriptionTier.free:
        return 0;
      case SubscriptionTier.habits:
        return 1;
      case SubscriptionTier.summit:
        return 2;
      case SubscriptionTier.clinical:
        return 3;
    }
  }

  // Check if this tier is higher than another
  bool isHigherThan(SubscriptionTier other) => tierLevel > other.tierLevel;

  // Check if this tier is lower than another
  bool isLowerThan(SubscriptionTier other) => tierLevel < other.tierLevel;

  // Get all tiers above this one
  List<SubscriptionTier> get higherTiers {
    return SubscriptionTier.values
        .where((tier) => tier.tierLevel > tierLevel)
        .toList();
  }

  // Get all tiers below this one
  List<SubscriptionTier> get lowerTiers {
    return SubscriptionTier.values
        .where((tier) => tier.tierLevel < tierLevel)
        .toList();
  }

  // Factory methods (using different names to avoid conflicts)
  static SubscriptionTier createFree() => SubscriptionTier.free;
  static SubscriptionTier createHabits() => SubscriptionTier.habits;
  static SubscriptionTier createSummit() => SubscriptionTier.summit;
  static SubscriptionTier createClinical() => SubscriptionTier.clinical;

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionTier defaultTier() => SubscriptionTier.free;
}
