enum SubscriptionPeriod {
  monthly('monthly'),
  yearly('yearly');

  const SubscriptionPeriod(this.value);
  final String value;

  static SubscriptionPeriod? fromString(String value) {
    for (SubscriptionPeriod period in SubscriptionPeriod.values) {
      if (period.value == value) {
        return period;
      }
    }
    return null;
  }

  // Helper methods
  bool get isMonthly => this == SubscriptionPeriod.monthly;
  bool get isYearly => this == SubscriptionPeriod.yearly;

  String get displayName {
    switch (this) {
      case SubscriptionPeriod.monthly:
        return 'Monthly';
      case SubscriptionPeriod.yearly:
        return 'Yearly';
    }
  }

  String get displayDuration {
    switch (this) {
      case SubscriptionPeriod.monthly:
        return 'Per month';
      case SubscriptionPeriod.yearly:
        return 'Per year';
    }
  }

  // Duration in months
  int get durationInMonths {
    switch (this) {
      case SubscriptionPeriod.monthly:
        return 1;
      case SubscriptionPeriod.yearly:
        return 12;
    }
  }

  // Calculate savings compared to monthly
  double calculateSavings(double monthlyPrice, double periodPrice) {
    if (isMonthly) return 0.0;
    final totalMonthlyPrice = monthlyPrice * durationInMonths;
    return ((totalMonthlyPrice - periodPrice) / totalMonthlyPrice) * 100;
  }

  // Factory methods (using different names to avoid conflicts)
  static SubscriptionPeriod createMonthly() => SubscriptionPeriod.monthly;
  static SubscriptionPeriod createYearly() => SubscriptionPeriod.yearly;

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionPeriod defaultPeriod() => SubscriptionPeriod.yearly;

  // Get all available periods
  static List<SubscriptionPeriod> allPeriods() => SubscriptionPeriod.values;

  // Get periods for UI toggle (monthly/yearly)
  static List<SubscriptionPeriod> togglePeriods() => [
        SubscriptionPeriod.monthly,
        SubscriptionPeriod.yearly,
      ];
}
