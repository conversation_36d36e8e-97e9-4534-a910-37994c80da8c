enum SubscriptionPeriod {
  monthly,
  yearly;

  // Get the string value for API/storage
  String get value {
    switch (this) {
      case SubscriptionPeriod.monthly:
        return 'monthly';
      case SubscriptionPeriod.yearly:
        return 'yearly';
    }
  }

  static SubscriptionPeriod? fromString(String value) {
    for (SubscriptionPeriod period in SubscriptionPeriod.values) {
      if (period.value == value) {
        return period;
      }
    }
    return null;
  }

  // Helper methods
  bool get isMonthly => this == SubscriptionPeriod.monthly;
  bool get isYearly => this == SubscriptionPeriod.yearly;

  // Duration in months
  int get durationInMonths {
    switch (this) {
      case SubscriptionPeriod.monthly:
        return 1;
      case SubscriptionPeriod.yearly:
        return 12;
    }
  }

  // Calculate savings compared to monthly
  double calculateSavings(double monthlyPrice, double periodPrice) {
    if (isMonthly) return 0.0;
    final totalMonthlyPrice = monthlyPrice * durationInMonths;
    return ((totalMonthlyPrice - periodPrice) / totalMonthlyPrice) * 100;
  }

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionPeriod get defaultPeriod => SubscriptionPeriod.yearly;

  // Get all available periods
  static List<SubscriptionPeriod> get allPeriods => SubscriptionPeriod.values;

  // Get periods for UI toggle (monthly/yearly)
  static List<SubscriptionPeriod> get togglePeriods => [
        SubscriptionPeriod.monthly,
        SubscriptionPeriod.yearly,
      ];
}
