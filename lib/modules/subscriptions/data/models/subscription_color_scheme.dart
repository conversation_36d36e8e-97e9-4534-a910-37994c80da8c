import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';

enum SubscriptionColorScheme {
  free('free'),
  habits('habits'),
  summit('summit'),
  clinical('clinical');

  const SubscriptionColorScheme(this.value);
  final String value;

  static SubscriptionColorScheme? fromString(String value) {
    for (SubscriptionColorScheme scheme in SubscriptionColorScheme.values) {
      if (scheme.value == value) {
        return scheme;
      }
    }
    return null;
  }

  // Get gradient colors for this scheme
  List<Color> get gradientColors {
    switch (this) {
      case SubscriptionColorScheme.free:
        return [AppColors.freeGradientStart, AppColors.freeGradientEnd];
      case SubscriptionColorScheme.habits:
        return [AppColors.habitsGradientStart, AppColors.habitsGradientEnd];
      case SubscriptionColorScheme.summit:
        return [AppColors.summitGradientStart, AppColors.summitGradientEnd];
      case SubscriptionColorScheme.clinical:
        return [AppColors.clinicalGradientStart, AppColors.clinicalGradientEnd];
    }
  }

  // Get primary color for this scheme
  Color get primaryColor => gradientColors.first;

  // Get secondary color for this scheme
  Color get secondaryColor => gradientColors.last;

  // Helper methods
  bool get isFree => this == SubscriptionColorScheme.free;
  bool get isHabits => this == SubscriptionColorScheme.habits;
  bool get isSummit => this == SubscriptionColorScheme.summit;
  bool get isClinical => this == SubscriptionColorScheme.clinical;

  // Factory methods (using different names to avoid conflicts)
  static SubscriptionColorScheme createFree() => SubscriptionColorScheme.free;
  static SubscriptionColorScheme createHabits() =>
      SubscriptionColorScheme.habits;
  static SubscriptionColorScheme createSummit() =>
      SubscriptionColorScheme.summit;
  static SubscriptionColorScheme createClinical() =>
      SubscriptionColorScheme.clinical;

  // Validation methods
  static bool isValid(String? value) {
    if (value == null) return false;
    return fromString(value) != null;
  }

  static SubscriptionColorScheme defaultScheme() =>
      SubscriptionColorScheme.free;

  // Parse from color names list (from API)
  static SubscriptionColorScheme? fromColorNames(List<String> colorNames) {
    if (colorNames.isEmpty) return null;

    // Extract scheme name from first color name
    // e.g., "freeGradientStart" -> "free"
    final firstColorName = colorNames.first.toLowerCase();

    if (firstColorName.startsWith('free')) {
      return SubscriptionColorScheme.free;
    } else if (firstColorName.startsWith('habits')) {
      return SubscriptionColorScheme.habits;
    } else if (firstColorName.startsWith('summit')) {
      return SubscriptionColorScheme.summit;
    } else if (firstColorName.startsWith('clinical')) {
      return SubscriptionColorScheme.clinical;
    }

    return null;
  }
}
