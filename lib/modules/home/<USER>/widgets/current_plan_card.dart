import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/core/ui/widgets/toasts.dart';

class CurrentPlanCard extends StatelessWidget {
  const CurrentPlanCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.L.currentPlanCardLabel,
          style: TextStyles.subheading2,
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surfacePrimary,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              width: 0.5,
              color: AppColors.stroke,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.dropShadow.withAlpha(30),
                spreadRadius: -4,
                blurRadius: 7,
                offset: Offset(2, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    // TODO: render using user's subscription status
                    "Freemium Version",
                    style: TextStyles.subheading1.copyWith(fontSize: 18),
                  ),
                  const Spacer(),
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.subscriptionFree,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      // TODO: render using user's subscription status
                      "Free",
                      style: TextStyles.buttonSmall.copyWith(
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.3,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 14),
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      // TODO: render using user's subscription status
                      text: "Unlock Premium: ",
                      style: TextStyles.heading5.copyWith(fontSize: 16),
                    ),
                    TextSpan(
                      text:
                          // TODO: render using user's subscription status
                          "Get personalized insights and exclusive features—upgrade now for the ultimate experience!",
                      style: TextStyles.body2,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 14),
              SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  onPressed: () => Toasts.showComingSoonToast(context),
                  height: 40,
                  child: Text("Upgrade"),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
