import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/constants/neuro.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/core/ui/widgets/dialogs.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/auth/ui/controllers/onboarding_controller.dart';
import 'package:neuroworld/modules/auth/ui/widgets/brain_assistant.dart';
import 'package:neuroworld/modules/goals/data/models/request/set_goal_request.dart';
import 'package:neuroworld/modules/goals/data/models/response/get_goal_response.dart';
import 'package:neuroworld/modules/goals/data/models/response/set_goal_response.dart';
import 'package:neuroworld/modules/goals/ui/controllers/goal_controller.dart';
import 'package:neuroworld/modules/notification/ui/widgets/notification_permission_dialog.dart';
import 'package:neuroworld/modules/onboarding/data/island_type.dart';
import 'package:neuroworld/modules/onboarding/ui/controllers/set_goal_controller.dart';
import 'package:neuroworld/modules/onboarding/ui/widget/day_selection.dart';
import 'package:neuroworld/modules/onboarding/ui/widget/goal_option.dart';
import 'package:neuroworld/modules/onboarding/ui/widget/text_bubble.dart';
import 'package:neuroworld/modules/onboarding/ui/widget/transparent_button.dart';
import 'package:neuroworld/modules/onboarding/ui/widget/unlocked_land.dart';
import 'package:permission_handler/permission_handler.dart';

class AddGoalScreen extends HookConsumerWidget {
  final String selectedIsland;

  const AddGoalScreen({super.key, required this.selectedIsland});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stepsState = useState<List<int>>([0]);
    final selectedDaysState = useState(1);
    final isConfirmButtonDisabledState = useState(false);
    final isSliderEnabledState = useState(true);
    final scrollController = useScrollController();
    final selectedGoalOptionIndex = useState<int?>(null);
    final changeNumberOfDaysClicked = useState(false);
    final changeGoalClicked = useState(false);
    final goalSet = useState(false);

    GetGoalsResponse goals =
        ref.read(onboardingControllerProvider).requireValue is GetGoalsResponse
            ? ref.read(onboardingControllerProvider).requireValue
                as GetGoalsResponse
            : ref.read(goalControllerProvider).requireValue as GetGoalsResponse;
    SetGoalResponse? setGoalResponse =
        ref.read(setGoalControllerProvider).requireValue is SetGoalResponse
            ? ref.read(setGoalControllerProvider).requireValue
            : null;

    final characterName = Neuro.values.byName(selectedIsland).characterName;

    final goalOptions = goals.goals
        .where((goal) => goal.category.name == selectedIsland)
        .map((goal) => goal.title)
        .toList();

    void scrollToBottom() {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutQuint,
        );
      }
    }

    useEffect(() {
      Future.delayed(const Duration(milliseconds: 500), scrollToBottom);
      return null;
    }, [stepsState.value]);

    void nextStep() {
      final next = stepsState.value.last + 1;
      if (next < 5) {
        stepsState.value = [...stepsState.value, next];
      }
    }

    void goToStep(int step) {
      if (step < 5) {
        stepsState.value = [...stepsState.value, step];
      }
    }

    void resetSteps() {
      stepsState.value = [0];
    }

    ref.easyListen(
      setGoalControllerProvider,
      loadingText: context.L.settingGoal,
      whenData: (state) async {
        if (state != null) {
          goalSet.value = true;
          if (await Permission.notification.status !=
              PermissionStatus.granted) {
            await Dialogs.showCustomDialog(
              // ignore: use_build_context_synchronously
              context,
              content: NotificationPermissionDialog(),
            );
          }

          nextStep();
        }
      },
    );

    void submitGoal({
      required int days,
      required String selectedGoalOption,
      required GetGoalsResponse goals,
    }) {
      final selectedGoal = goals.goals.firstWhere(
        (goal) => goal.title == selectedGoalOption,
        orElse: () => throw Exception('Goal not found'),
      );

      final matchingVersion = selectedGoal.goalVersions?.firstWhere(
            (version) => version.level == 'Beginner',
            orElse: () => throw Exception('Matching intensity level not found'),
          ) ??
          (throw Exception('Goal versions are null'));

      final goalVersionId = matchingVersion.id;

      ref.read(setGoalControllerProvider.notifier).setGoal(
            request: SetGoalRequest(
              goalVersionId: goalVersionId,
              target: days,
            ),
          );
    }

    SvgGenImage getIslandSvgPath(String selectedIsland) {
      final islandCategory = Neuro.values.firstWhere(
        (e) => e.name.toLowerCase() == selectedIsland.toLowerCase(),
        orElse: () => throw Exception('Invalid island selected'),
      );

      switch (islandCategory) {
        case Neuro.nutrition:
          return Assets.svgs.onboarding.nuri;
        case Neuro.exercise:
          return Assets.svgs.onboarding.spark;
        case Neuro.unwind:
          return Assets.svgs.onboarding.flo;
        case Neuro.restore:
          return Assets.svgs.onboarding.owl;
        case Neuro.optimize:
          return Assets.svgs.onboarding.sophie;
      }
    }

    String nextButtonText(List<int> steps, String characterName) {
      if (steps.last == 0) return 'Let\'s meet $characterName';
      if (steps.last == 1) return context.L.letsGo;
      if (steps.last == 4) return context.L.exploreCity;
      return context.L.next;
    }

    Widget stepOne(String characterName) {
      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: ChatBubbleWithAvatar(
              avatar: Assets.svgs.onboarding.brainAssistant.svg(
                height: 60,
                width: 60,
              ),
              avatarPosition: AvatarPosition.leftBottom,
              child: Text(
                "I want you to meet $characterName. ${context.L.onboardingStepOne}",
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      );
    }

    Widget stepTwo(String characterName) {
      return Column(
        children: [
          TextBubble(message: 'Let\'s meet $characterName'),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: ChatBubbleWithAvatar(
              child: Text(
                "Hi, I'm $characterName! I'll be your guide to making better $selectedIsland choices.",
              ),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: ChatBubbleWithAvatar(
              avatar: getIslandSvgPath(selectedIsland).svg(
                height: 58,
                width: 58,
              ),
              avatarPosition: AvatarPosition.leftCenter,
              child: Text(context.L.strongMind),
            ),
          ),
          const SizedBox(height: 16),
        ],
      );
    }

    Widget stepThree(List<String> goalOptions) {
      return Column(
        children: [
          if (changeGoalClicked.value == true &&
              selectedGoalOptionIndex.value == null) ...[
            GoalOption(
              avatarPath: getIslandSvgPath(selectedIsland).path,
              avatarSize: 58,
              options: goalOptions,
              onSelected: (index) {
                changeGoalClicked.value = false;
                selectedGoalOptionIndex.value = index;
                nextStep();
              },
            ),
          ] else ...[
            TextBubble(message: context.L.letsGo),
            const SizedBox(height: 16),
            GoalOption(
              avatarPath: getIslandSvgPath(selectedIsland).path,
              avatarSize: 58,
              options: goalOptions,
              onSelected: (index) {
                changeGoalClicked.value = false;
                selectedGoalOptionIndex.value = index;
                nextStep();
              },
            ),
          ],
          const SizedBox(height: 16),
        ],
      );
    }

    Widget stepFour(String selectedGoalOption, GetGoalsResponse goals) {
      return Column(
        children: [
          if (changeNumberOfDaysClicked.value == true) ...[
            DaysSelection(
              avatarPath: getIslandSvgPath(selectedIsland).path,
              avatarPosition: AvatarPosition.leftBottom,
              confirmButtonText: context.L.confirmDays,
              onConfirm: (val) {
                selectedDaysState.value = val;
                submitGoal(
                  days: val,
                  selectedGoalOption: selectedGoalOption,
                  goals: goals,
                );
              },
            ),
            const SizedBox(height: 16),
          ] else ...[
            TextBubble(message: selectedGoalOption),
            const SizedBox(height: 16),
            TransparentButton(
                text: context.L.changeGoal,
                onPressed: () {
                  if (!goalSet.value && selectedGoalOptionIndex.value != null) {
                    changeGoalClicked.value = true;
                    selectedGoalOptionIndex.value = null;
                    goToStep(2);
                  }
                }),
            const SizedBox(height: 16),
            TransparentButton(
                text: context.L.changeIsland,
                onPressed: () {
                  resetSteps();
                  selectedDaysState.value = 0;
                  isConfirmButtonDisabledState.value = false;
                  isSliderEnabledState.value = true;
                  context.pop();
                }),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: ChatBubbleWithAvatar(
                child: Text(context.L.greatJob),
              ),
            ),
            const SizedBox(height: 16),
            DaysSelection(
              avatarPath: getIslandSvgPath(selectedIsland).path,
              avatarPosition: AvatarPosition.leftBottom,
              confirmButtonText: context.L.confirmDays,
              onConfirm: (val) {
                selectedDaysState.value = val;
                submitGoal(
                  days: val,
                  selectedGoalOption: selectedGoalOption,
                  goals: goals,
                );
              },
            ),
            const SizedBox(height: 16),
          ],
        ],
      );
    }

    Widget stepFive(String selectedGoalOption, GetGoalsResponse goals,
        int selectedIntensityOption, SetGoalResponse? setGoalResponse) {
      return Column(
        children: [
          TextBubble(message: '${selectedDaysState.value} days'),
          const SizedBox(height: 16),
          BrainAssistant(
            avatarPosition: AvatarPosition.leftBottom,
            avatarPath: getIslandSvgPath(selectedIsland).path,
            avatarSize: 58,
            message:
                'Well done ${setGoalResponse != null ? setGoalResponse.user.profile.fullName : ''}! Your goal is all set.',
          ),
          const SizedBox(height: 16),
          UnlockedLand(
            message: context.L.congratulationsUnlock,
            imagePath: Assets.svgs.onboarding.land.path,
          ),
          const SizedBox(height: 16),
          BrainAssistant(
            avatarPosition: AvatarPosition.leftBottom,
            avatarPath: Assets.svgs.onboarding.brainAssistant.path,
            avatarSize: 60,
            message: context.L.explore,
          ),
          const SizedBox(height: 16),
        ],
      );
    }

    Widget getStepWidget(
        int step,
        List<String> goalOptions,
        String selectedGoalOption,
        GetGoalsResponse goals,
        int selectedIntensityOption,
        SetGoalResponse? setGoalResponse,
        String characterName,
        int selectedDaysState,
        bool isConfirmButtonDisabledState,
        bool isSliderEnabledState) {
      switch (step) {
        case 0:
          return stepOne(characterName);
        case 1:
          return stepTwo(characterName);
        case 2:
          return stepThree(goalOptions);
        case 3:
          return stepFour(selectedGoalOption, goals);
        case 4:
          return stepFive(selectedGoalOption, goals, selectedIntensityOption,
              setGoalResponse);
        default:
          return const SizedBox.shrink();
      }
    }

    Widget buildNextButton(
        List<int> steps,
        String characterName,
        int selectedDaysState,
        bool isConfirmButtonDisabledState,
        bool isSliderEnabledState) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Divider(height: 1, color: AppColors.stroke),
          Padding(
            padding: const EdgeInsets.fromLTRB(30, 16, 30, 34),
            child: Row(
              children: [
                Expanded(
                  child: PrimaryButton(
                    onPressed: () {
                      if (steps.last == 4 && goalSet.value) {
                        selectedDaysState = 0;
                        isConfirmButtonDisabledState = false;
                        isSliderEnabledState = true;
                        context.go(GoalsRoute().location);
                      } else {
                        nextStep();
                      }
                    },
                    child: Text(nextButtonText(steps, characterName)),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: Assets.svgs.onboarding.crossIcon.svg(width: 31, height: 40),
          onPressed: () {
            resetSteps();
            selectedDaysState.value = 0;
            isConfirmButtonDisabledState.value = false;
            isSliderEnabledState.value = true;
            context.pop();
          },
        ),
        title: Text(
          'Set your ${selectedIsland[0].toUpperCase()}${selectedIsland.substring(1)} goal',
          style: TextStyles.subheading1.copyWith(color: AppColors.textPrimary),
        ),
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Divider(height: 1, color: AppColors.stroke),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                controller: scrollController,
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: stepsState.value
                        .map((step) => getStepWidget(
                            step,
                            goalOptions,
                            goalOptions[selectedGoalOptionIndex.value ?? 0],
                            goals,
                            0,
                            setGoalResponse,
                            characterName,
                            selectedDaysState.value,
                            isConfirmButtonDisabledState.value,
                            isSliderEnabledState.value))
                        .toList(),
                  ),
                ),
              ),
            ),
            if (stepsState.value.last == 0 ||
                stepsState.value.last == 1 ||
                stepsState.value.last == 4)
              buildNextButton(
                  stepsState.value,
                  characterName,
                  selectedDaysState.value,
                  isConfirmButtonDisabledState.value,
                  isSliderEnabledState.value),
          ],
        ),
      ),
    );
  }
}
