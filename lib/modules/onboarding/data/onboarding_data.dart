import 'package:flutter/material.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';

enum OnboardingStepType {
  standard,
  questionnaire,
  questionnaireCircular,
  tooltip,
  mapView
}

class TooltipData {
  final String content;
  final Alignment position;
  final Widget target;
  final double width;
  final double height;

  const TooltipData({
    required this.content,
    required this.position,
    required this.target,
    this.width = 300.0,
    this.height = 173.0,
  });
}

enum OnboardingData {
  step1(
    type: OnboardingStepType.standard,
    animation: 'assets/lottie/party_popper.json',
    questions: [],
    tooltip: null,
  ),
  step2(
    type: OnboardingStepType.standard,
    animation: 'assets/lottie/myla.json',
    questions: [],
    tooltip: null,
  ),
  step3(
    type: OnboardingStepType.mapView,
    animation: '',
    questions: [],
    tooltip: null,
  );

  const OnboardingData({
    required this.type,
    required this.animation,
    required this.questions,
    required this.tooltip,
  });

  final OnboardingStepType type;
  final String animation;
  final List<String> questions;
  final TooltipData? tooltip;

  /// Get localized title
  String title(BuildContext context) {
    return switch (this) {
      OnboardingData.step1 => context.L.onboardingStepOneTitle,
      OnboardingData.step2 => context.L.onboardingStepTwoTitle,
      OnboardingData.step3 => context.L.selectIsland,
    };
  }

  /// Get localized description
  String description(BuildContext context) {
    return switch (this) {
      OnboardingData.step1 => context.L.onboardingStepOneDescription,
      OnboardingData.step2 => context.L.onboardingStepTwoDescription,
      OnboardingData.step3 => context.L.onboardingStepThreeDescription,
    };
  }

  /// Get localized button text
  String buttonText(BuildContext context) {
    return switch (this) {
      OnboardingData.step1 => context.L.stepOneButton,
      OnboardingData.step2 => context.L.letsGetStarted,
      OnboardingData.step3 => context.L.next,
    };
  }

  static OnboardingData forStep(int step) {
    return switch (step) {
      0 => OnboardingData.step1,
      1 => OnboardingData.step2,
      2 => OnboardingData.step3,
      _ => throw ArgumentError('Invalid onboarding step: $step'),
    };
  }
}
