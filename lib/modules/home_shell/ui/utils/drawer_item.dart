import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';

enum DrawerItem {
  profile,
  susbcription,
  feedback,
  deleteAccount,
  logout;

  const DrawerItem();

  Widget get icon =>
      RoundedIcon(size: 33, child: SvgPicture.asset(_getDrawerIconPath()));

  Widget? get trailing => this == DrawerItem.susbcription
      ? Container(
          decoration: BoxDecoration(
            color: AppColors.subscriptionFree,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            // TODO: render using user's subscription status
            "Free",
            style: TextStyles.buttonSmall.copyWith(
              fontWeight: FontWeight.w600,
              letterSpacing: -0.3,
              color: Colors.white,
            ),
          ),
        )
      : null;

  String getDrawerItemLabel(BuildContext context) {
    return switch (this) {
      DrawerItem.profile => context.L.menuItemProfile,
      DrawerItem.susbcription => context.L.menuItemSubscription,
      DrawerItem.feedback => context.L.menuItemFeedback,
      DrawerItem.deleteAccount => context.L.menuItemDeleteAccount,
      DrawerItem.logout => context.L.menuItemLogout,
    };
  }

  String _getDrawerIconPath() {
    return switch (this) {
      DrawerItem.profile => Assets.svgs.menuDrawer.profile.path,
      DrawerItem.susbcription => Assets.svgs.menuDrawer.subscription.path,
      DrawerItem.feedback => Assets.svgs.menuDrawer.feedback.path,
      DrawerItem.deleteAccount => Assets.svgs.menuDrawer.deleteAccount.path,
      DrawerItem.logout => Assets.svgs.menuDrawer.logout.path,
    };
  }
}
