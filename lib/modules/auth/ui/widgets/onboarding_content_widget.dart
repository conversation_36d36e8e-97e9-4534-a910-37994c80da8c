import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/modules/auth/ui/widgets/island_map_widget.dart';
import 'package:neuroworld/modules/goals/data/island_category_data.dart';
import 'package:neuroworld/modules/onboarding/data/onboarding_data.dart';

class OnboardingContent extends ConsumerWidget {
  final OnboardingData onboardingData;
  final bool? isAddGoal;
  final List<String>? disabledIslands;
  final List<IslandCategory> allGoals;

  const OnboardingContent({
    super.key,
    required this.onboardingData,
    this.isAddGoal,
    this.disabledIslands,
    required this.allGoals,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (onboardingData.animation.isNotEmpty)
          SizedBox(
            height: 99,
            width: 104,
            child: Lottie.asset(
              onboardingData.animation,
              height: 250,
              width: 250,
              fit: BoxFit.cover,
            ),
          ),
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 34),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (isAddGoal == null)
                Column(
                  children: [
                    Text(
                      onboardingData.title(context),
                      textAlign: TextAlign.center,
                      style: TextStyles.heading3.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      onboardingData.description(context),
                      textAlign: TextAlign.center,
                      style: TextStyles.body1.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                )
            ],
          ),
        ),
        if (onboardingData.type == OnboardingStepType.mapView) ...[
          IslandMapWidget(disabledIslands: disabledIslands, allGoals: allGoals),
        ],
      ],
    );
  }
}
