import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/extensions/ref_extension.dart';
import 'package:neuroworld/core/infrastructure/navigation/router.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/auth/ui/controllers/onboarding_controller.dart';
import 'package:neuroworld/modules/auth/ui/providers/selected_island_provider.dart';
import 'package:neuroworld/modules/auth/ui/widgets/brain_assistant.dart';
import 'package:neuroworld/modules/auth/ui/widgets/custom_progressbar.dart';
import 'package:neuroworld/modules/auth/ui/widgets/onboarding_content_widget.dart';
import 'package:neuroworld/modules/goals/data/island_category_data.dart';
import 'package:neuroworld/modules/onboarding/data/onboarding_data.dart';

class OnboardingScreen extends HookConsumerWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final step = useState(0);
    final allGoals = getIslandCategories(context);

    void incrementStep() {
      if (step.value < OnboardingData.values.length - 1) {
        step.value++;
      }
    }

    final onboardingData = OnboardingData.forStep(step.value);

    ref.easyListen(
      onboardingControllerProvider,
      loadingText: context.L.gettingGoal,
      whenData: (state) {
        if (state != null) {
          incrementStep();
        }
      },
    );

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              Assets.images.bg.path,
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 79),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 30,
              ),
              child: CustomProgressBar(
                progress: (step.value + 1) / OnboardingData.values.length,
                duration: const Duration(milliseconds: 500),
              ),
            ),
            Expanded(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 500),
                transitionBuilder: (child, animation) => FadeTransition(
                  opacity: animation,
                  child: child,
                ),
                child: Padding(
                  key: ValueKey<int>(step.value),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 0.0,
                  ),
                  child: Center(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.only(top: 0),
                      child: OnboardingContent(
                        onboardingData: onboardingData,
                        allGoals: allGoals,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            if (onboardingData.type == OnboardingStepType.mapView) ...[
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.stroke,
                ),
                width: double.infinity,
                height: 1.5,
              ),
              const SizedBox(height: 16),
              BrainAssistant(
                buttonTitle: context.L.askMe,
                message: context.L.letMeKnow,
                avatarPath: Assets.svgs.onboarding.brainAssistant.path,
                avatarPosition: AvatarPosition.leftCenter,
                avatarSize: 86,
                onTap: () {
                  ChatRoute().push(context);
                },
              ),
            ],
            Padding(
              padding: const EdgeInsets.fromLTRB(30, 24, 30, 64),
              child: Row(
                children: [
                  const SizedBox(width: 10),
                  Expanded(
                    child: Consumer(
                      builder: (context, ref, child) {
                        final bool isMapView =
                            onboardingData.type == OnboardingStepType.mapView;

                        final selectedIsland = isMapView
                            ? ref.watch(selectedIslandProvider)
                            : null;

                        return PrimaryButton(
                          onPressed: () {
                            if (step.value < OnboardingData.values.length - 1) {
                              if (step.value == 1) {
                                ref
                                    .read(onboardingControllerProvider.notifier)
                                    .getGoals();
                              } else {
                                incrementStep();
                              }
                            }
                            if (selectedIsland != null) {
                              context.push(
                                OnboardingAddGoalRoute().location,
                                extra: selectedIsland,
                              );
                            }
                          },
                          disabled: isMapView && selectedIsland == null,
                          child: Text(onboardingData.buttonText(context)),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
