import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/chat/data/models/action_type.dart';
import 'package:neuroworld/modules/chat/ui/controllers/cta_controller.dart';

class CtaAction extends ConsumerWidget {
  const CtaAction({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return PrimaryButton(
      onPressed: () async =>
          ref.read(cTAControllerProvider.notifier).refresh(ActionType.cta),
      child: Text(context.L.chatCTAButton),
    );
  }
}
