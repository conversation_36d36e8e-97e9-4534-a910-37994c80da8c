import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:neuroworld/core/infrastructure/extensions/date_extensions.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/goals/data/models/streak_info.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:neuroworld/modules/goals/ui/providers/selected_date_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'daily_tracking_list_provider.g.dart';

@riverpod
class DailyTrackingList extends _$DailyTrackingList {
  @override
  Future<List<DailyTracking>> build() async {
    state = const AsyncLoading();
    final DateTime date = ref.watch(selectedDateProvider);
    final monthlyTracking = await ref.watch(
      getMonthlyTrackingProvider(date.month, date.year).future,
    );

    final goals = ref.read(authStateProvider)!.user.selectedGoals;

    final IMap<String, SelectedGoal> goalsMap =
        IMap.fromValues(values: goals, keyMapper: (goal) => goal.id);
    final selectedIndex = date.weekday % 7;
    final List<DailyTracking> dailyTracking = [];

    for (final goal in goalsMap.toValueList()) {
      if (goal.firstTrackedDate == null ||
          goal.firstTrackedDate!.isAfter(date)) {
        continue;
      }
      final offset = goal.startDate!.weekday % 7;
      final startIndex = (selectedIndex - offset) % 7;

      // we give extra threshold 4hrs on either side to match all inclusive cases for
      // isBefore and isAfter and also to avoid any DST related bugs
      final startDate = date.subtract(Duration(days: startIndex, hours: 4));
      final endDate = date.add(Duration(days: 6 - startIndex, hours: 4));

      StreakInfo? streakInfo = goal.getStreakInfo(date);

      dailyTracking.add(
        DailyTracking(
          currentIndex: startIndex,
          startDate: startDate,
          endDate: endDate,
          selectedGoal: goal,
          trackedDates: [],
          weeklyCheckInTracking: [],
          isNewWeek: streakInfo.isNewWeek,
          shouldResetGoal: streakInfo.isStreakBroken,
          gracePeriodDays: streakInfo.gracePeriodDays,
        ),
      );
    }

    final map = IMap.fromValues(
      keyMapper: (dailyTracking) => dailyTracking.selectedGoal.id,
      values: dailyTracking,
    );

    // assuming we fetch a whole month's tracking at once,
    // we don't want to loop over the dates outside the bounds of our current selected week,
    // so we set bounds. bounds are +/- 6 days because thats the maximum offset
    // an individual goal's timeline can have based on current selectedDate

    // we also add a 4 hour buffer in bounds to match inclusively
    // for isBefore and isAfter and avoid DST related bugs (same as above)
    final lowerBound = date.subtract(Duration(days: 6, hours: 4));
    final upperBound = date.add(Duration(days: 6, hours: 4));

    for (final tracking in monthlyTracking) {
      // ignore dates outside bounds
      if (tracking.date.isBefore(lowerBound)) continue;
      if (tracking.date.isAfter(upperBound)) break;

      final goal = map.get(tracking.selectedGoal);
      if (goal == null) continue;

      // create tracked date array (can we optimize here further??)
      if ((tracking.date.isAfter(goal.startDate!)) &&
          tracking.date.isBefore(goal.endDate!)) {
        goal.trackedDates.add(tracking.date);
      }
    }

    for (final tracking in dailyTracking) {
      // add 5 hours to range's startDate to re-offset it back to actual startDate
      // instead of 1 day before (to faciliate isAfter check)
      // note: here we dont care about time, our isSameDate extension only matches the date
      final startDateInclusive = tracking.startDate!.add(Duration(hours: 5));
      // generate final list of bools where true = tracked, false = not tracked
      // this is the actual state value that our UI will consume
      final goal = map.get(tracking.selectedGoal.id);
      if (goal == null) break;
      goal.weeklyCheckInTracking.addAll(List.generate(
        7,
        // can we optimize this? dont need to always look match all trackedDates
        // eg. if date goes after DateTime.now we can stop searching
        // similarly if date is already marked true maybe we can remove it from list
        // so it doesn't try to get re-matched
        (index) => tracking.trackedDates.any(
          (marked) => marked.isSameDate(
            startDateInclusive.add(Duration(days: index)),
          ),
        ),
      ));
    }
    return map.toValueList();
  }
}
