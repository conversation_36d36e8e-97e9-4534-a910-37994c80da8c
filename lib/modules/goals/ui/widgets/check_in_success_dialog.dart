import 'dart:math';

import 'package:fast_immutable_collections/fast_immutable_collections.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/chat_bubble.dart';
import 'package:neuroworld/core/ui/widgets/primary_button.dart';
import 'package:neuroworld/modules/goals/data/models/award_type.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';

class CheckInSuccessDialog extends StatelessWidget {
  const CheckInSuccessDialog({
    super.key,
    required this.selectedGoal,
    required this.awardedPoints,
    required this.totalPoints,
  });

  final SelectedGoal selectedGoal;
  final Map<String, int> awardedPoints;
  final int totalPoints;

  @override
  Widget build(BuildContext context) {
    final months = selectedGoal.currentWeek ~/ 4;
    final weeks = selectedGoal.currentWeek % 4;
    final awards = awardedPoints.entries.asList();
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Stack(
          alignment: Alignment.topCenter,
          children: [
            Column(
              children: [
                const SizedBox(height: 44),
                Container(
                  padding: const EdgeInsets.all(20),
                  margin: const EdgeInsets.symmetric(horizontal: 24),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: AppColors.surfaceSecondary,
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 22),
                      Text(
                        selectedGoal.goal.title,
                        style: TextStyles.heading3,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      const Divider(thickness: 0.5, color: AppColors.stroke),
                      const SizedBox(height: 8),
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  "You've been ${selectedGoal.goal.verb.toLowerCase()} for:\n",
                              style: TextStyles.body1,
                            ),
                            if (months > 0)
                              TextSpan(
                                text:
                                    "$months ${months == 1 ? "week" : "weeks"}, ",
                                style: TextStyles.subheading2
                                    .copyWith(height: 1.8),
                              ),
                            TextSpan(
                              text: "$weeks ${weeks == 1 ? "week" : "weeks"}!",
                              style:
                                  TextStyles.subheading2.copyWith(height: 1.8),
                            )
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(height: 0.75, color: AppColors.stroke),
                      ListView.separated(
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          final award =
                              AwardType.bySnakeCaseName(awards[index].key);
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Row(
                              children: [
                                award.icon,
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    award.getDisplayText(context),
                                    style: TextStyles.body1,
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.dropShadow,
                                        spreadRadius: -1.5,
                                        blurRadius: 5,
                                        offset: Offset(0, 4),
                                      ),
                                      BoxShadow(
                                        color: AppColors.dropShadow,
                                        spreadRadius: -3,
                                        blurRadius: 7,
                                        offset: Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Assets.svgs.neuroPoint.svg(),
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  awards[index].value.toString(),
                                  style: TextStyles.subheading2,
                                )
                              ],
                            ),
                          );
                        },
                        separatorBuilder: (context, index) =>
                            Container(height: 0.75, color: AppColors.stroke),
                        itemCount: awardedPoints.values.length,
                      ),
                      Container(height: 0.75, color: AppColors.stroke),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: PrimaryButton(
                          onPressed: () => NavService.popDialog(context),
                          child: Text(context.L.checkInSuccessButton),
                        ),
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 300),
              ],
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(width: 4, color: AppColors.surfaceSecondary),
                borderRadius: BorderRadius.circular(100),
              ),
              child: Container(
                height: 72,
                width: 72,
                decoration: BoxDecoration(
                  color: AppColors.surfaceSecondary,
                  border: Border.all(
                    width: 4,
                    color: selectedGoal.goal.category.color,
                  ),
                  borderRadius: BorderRadius.circular(100),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Transform.scale(
                      scale: 1.5,
                      child: SvgPicture.asset(
                        '${Assets.svgs.goals.path}/${selectedGoal.goal.imageUrl}.svg',
                      ),
                    ),
                    if (selectedGoal.goal.imageUrl.startsWith('x_'))
                      Transform.rotate(
                        angle: -pi / 4,
                        child: Container(
                          height: double.infinity,
                          width: 4,
                          color: selectedGoal.goal.category.color,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                height: 270,
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Expanded(
                      child: Column(
                        children: [selectedGoal.goal.category.characterIcon],
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(right: 24),
                        child: Column(
                          children: [
                            ChatBubble(
                              toolTipAlignment: Alignment.bottomLeft,
                              child: Text(
                                  'Congrats! That\'s $totalPoints NEURO Points, total. Huge!'),
                            ),
                            const Spacer(),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}
