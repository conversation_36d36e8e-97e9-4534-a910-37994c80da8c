import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/assets.gen.dart';
import 'package:neuroworld/core/infrastructure/navigation/nav_service.dart';
import 'package:neuroworld/core/l10n/app_locale.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';
import 'package:neuroworld/core/ui/widgets/drawers.dart';
import 'package:neuroworld/core/ui/widgets/rounded_icon.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_info_header.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/edit_goal_sheet.dart';
import 'package:neuroworld/modules/goals/ui/widgets/goal_tracking_card/goal_details_sheet.dart';

class GoalsMoreSheet extends StatelessWidget {
  const GoalsMoreSheet({
    super.key,
    required this.initialValue,
    required this.selectedGoal,
  });

  final int initialValue;
  final SelectedGoal selectedGoal;

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap: true,
      children: [
        const SizedBox(height: 20),
        GoalInfoHeader(goal: selectedGoal.goal),
        const SizedBox(height: 12),
        ListTile(
          contentPadding: EdgeInsets.zero,
          onTap: () {
            NavService.popDialog(context);
            Drawers.showCustomBottomSheet(
              context,
              child: EditGoalSheet(
                initialValue: initialValue,
                selectedGoal: selectedGoal,
              ),
            );
          },
          leading: RoundedIcon(
            size: 33,
            child: Assets.svgs.edit.svg(),
          ),
          title: Text(
            context.L.goalsMoreEditOption,
            style: TextStyles.body1,
          ),
        ),
        ListTile(
          contentPadding: EdgeInsets.zero,
          onTap: () {
            NavService.popDialog(context);
            Drawers.showCustomBottomSheet(
              context,
              child: GoalDetailsSheet(goal: selectedGoal.goal),
            );
          },
          leading: RoundedIcon(
            size: 33,
            child: Assets.svgs.info.svg(width: 30, height: 30),
          ),
          title: Text(
            context.L.goalsMoreDetailsOption,
            style: TextStyles.body1,
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
