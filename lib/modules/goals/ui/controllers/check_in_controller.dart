import 'package:neuroworld/core/infrastructure/error/app_exceptions.dart';
import 'package:neuroworld/modules/auth/data/services/auth_service.dart';
import 'package:neuroworld/modules/auth/ui/providers/auth_state_provider.dart';
import 'package:neuroworld/modules/goals/data/models/check_in.dart';
import 'package:neuroworld/modules/goals/data/models/daily_tracking.dart';
import 'package:neuroworld/modules/goals/data/services/goals_service.dart';
import 'package:neuroworld/modules/goals/ui/providers/recently_tracked_goals_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'check_in_controller.g.dart';

@riverpod
class CheckInController extends _$CheckInController {
  @override
  FutureOr<CheckIn?> build() => null;

  Future<bool> checkIn(DailyTracking dailyTracking) async {
    state = const AsyncLoading();
    try {
      final checkIn =
          await ref.watch(goalsServiceProvider).checkIn(dailyTracking);
      state = AsyncData(checkIn);
      ref
          .read(recentlyTrackedGoalsProvider.notifier)
          .addGoalId(checkIn.selectedGoal.id);
      return true;
    } catch (err, stack) {
      if (err is ServerException && err.code == 403) {
        // 403 on check-in indicates user has stale info, re-fetch from /me api
        final auth = await ref.watch(authServiceProvider).refreshUser();
        ref.read(authStateProvider.notifier).login(auth);
        ref.invalidate(getMonthlyTrackingProvider);
        ref.invalidate(getTodaysTrackingProvider);
      }
      state = AsyncValue.error(err, stack);
      return false;
    }
  }

  void resetState() => state = AsyncData(null);
}
