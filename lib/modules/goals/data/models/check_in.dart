import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neuroworld/modules/auth/data/models/response/auth.dart';
import 'package:neuroworld/modules/goals/data/models/selected_goal.dart';

part 'check_in.freezed.dart';

@freezed
abstract class CheckIn with _$CheckIn {
  const factory CheckIn({
    required Auth auth,
    required Map<String, int> awardedPoints,
    required SelectedGoal selectedGoal,
  }) = _CheckIn;
}
