import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:neuroworld/core/data/sources/shared_preferences.dart';
import 'package:neuroworld/core/infrastructure/extensions/future_extensions.dart';
import 'package:neuroworld/core/l10n/app_locale_controller.dart';
import 'package:neuroworld/core/ui/providers/package_info_provider.dart';
import 'package:neuroworld/modules/auth/ui/providers/check_auth_provider.dart';
import 'package:neuroworld/modules/notification/data/providers/fcm_providers.dart';
import 'package:neuroworld/modules/notification/data/services/messaging_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_init_provider.g.dart';

@Riverpod(keepAlive: true)
Future<void> appInit(Ref ref) async {
  ref.onDispose(() {
    // ensure we invalidate all the providers we depend on
    // (no need to invalidate AutoDisposeProviders)
    ref.invalidate(sharedPreferencesProvider);
    ref.invalidate(appLocaleControllerProvider);
  });

  ref.watch(checkAuthProvider);

  // all asynchronous app initialization code:
  final min = Future<void>.delayed(
    const Duration(milliseconds: 250), // splash min time
  );
  final s1 = ref.watch(appLocaleControllerProvider.future);
  final s2 = ref.watch(packageInfoProvider.future);
  final s3 = ref.watch(setupFlutterNotificationsProvider.future);
  final s4 = ref.watch(fcmTokenProvider.future);
  await [min, s1, s2, s3, s4].wait.throwAllErrors();
}
