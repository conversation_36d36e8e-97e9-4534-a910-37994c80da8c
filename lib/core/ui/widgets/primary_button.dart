import 'package:flutter/material.dart';
import 'package:neuroworld/core/constants/gen/colors.gen.dart';
import 'package:neuroworld/core/ui/theme/text_styles.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.child,
    required this.onPressed,
    this.disabled = false,
    this.height,
    this.textStyle,
    this.backgroundColor,
    this.borderColor,
  });

  final void Function() onPressed;
  final Widget child;
  final bool disabled;
  final double? height;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? borderColor;

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: disabled ? 0.7 : 1,
      child: Container(
        height: height ?? 47,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border(
            bottom: BorderSide(
                color: borderColor ?? AppColors.primaryButtonShadow, width: 3),
          ),
        ),
        child: IntrinsicHeight(
          child: TextButton(
            onPressed: () {
              if (!disabled) onPressed();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: backgroundColor ?? AppColors.boldOrange,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              textStyle:
                  TextStyles.buttonLarge.copyWith(height: 1, fontSize: 17.5),
            ),
            child: Container(margin: EdgeInsets.only(top: 3), child: child),
          ),
        ),
      ),
    );
  }
}
