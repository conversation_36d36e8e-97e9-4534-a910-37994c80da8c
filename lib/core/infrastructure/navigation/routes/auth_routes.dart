part of '../router.dart';

@TypedGoRoute<LandingRoute>(path: '/landing')
class LandingRoute extends GoRouteData {
  const LandingRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) =>
      FadeTransitionPage(state.pageKey, const LandingScreen());
}

@TypedGoRoute<LoginRoute>(path: '/login')
class LoginRoute extends GoRouteData {
  const LoginRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const LoginScreen();
}

@TypedGoRoute<SignupRoute>(path: '/signup')
class SignupRoute extends GoRouteData {
  const SignupRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const SignupScreen();
}

@TypedGoRoute<ForgotPasswordRoute>(path: '/forgot-password')
class ForgotPasswordRoute extends GoRouteData {
  const ForgotPasswordRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const ForgotPasswordScreen();
}

@TypedGoRoute<TermsAndConditionsRoute>(path: '/terms-and-conditions')
class TermsAndConditionsRoute extends GoRouteData {
  const TermsAndConditionsRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const TermsAndConditionsScreen();
}

@TypedGoRoute<OnboardingRoute>(path: '/onboarding')
class OnboardingRoute extends GoRouteData {
  const OnboardingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return OnboardingScreen();
  }
}

@TypedGoRoute<OnboardingAddGoalRoute>(path: '/add-goal')
class OnboardingAddGoalRoute extends GoRouteData {
  const OnboardingAddGoalRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final selectedIsland = state.extra as String? ?? 'defaultIsland';

    return AddGoalScreen(selectedIsland: selectedIsland);
  }
}

@TypedGoRoute<PrivacyPolicyRoute>(path: '/privacy-policy')
class PrivacyPolicyRoute extends GoRouteData {
  const PrivacyPolicyRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const PrivacyPolicyScreen();
}

@TypedGoRoute<LandingSignupScreenRoute>(path: '/landing-signup')
class LandingSignupScreenRoute extends GoRouteData {
  const LandingSignupScreenRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const LandingSignupScreen();
}

// TODO: refactor this into abstract DeepLinkRoute class that can be re-used
@TypedGoRoute<ResetPasswordRoute>(
  path: '/reset-password/:token',
)
class ResetPasswordRoute extends GoRouteData {
  final String token;

  const ResetPasswordRoute({required this.token});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SplashScreen(
      redirect: ResetPasswordInternalRoute(token: token).location,
    );
  }
}

@TypedGoRoute<ResetPasswordInternalRoute>(
  path: '/reset-password-internal/:token',
)
class ResetPasswordInternalRoute extends GoRouteData {
  final String token;

  const ResetPasswordInternalRoute({required this.token});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ResetPasswordScreen(token: token);
  }
}
