part of 'router.dart';

extension GoRouterStateX on GoRouterState {
  String get routeLocation => uri.toString();

  List<NavPermissions> get navPermissions {
    const defaultPermissions = [NavPermissions.user, NavPermissions.admin];
    if (routeLocation.startsWith(const SplashRoute().location)) {
      return NavPermissions.values;
    }

    final publicRoutes = [
      const LandingRoute().location,
      const LoginRoute().location,
      const SignupRoute().location,
      const ForgotPasswordRoute().location,
      const TermsAndConditionsRoute().location,
      const OnboardingRoute().location,
      const DeleteAccountSuccessRoute().location,
      const PrivacyPolicyRoute().location,
      const LandingSignupScreenRoute().location,
      const ResetPasswordRoute(token: '').location,
    ];

    if (routeLocation.contains('/reset-password') ||
        publicRoutes.any(routeLocation.startsWith)) {
      return [NavPermissions.unauthenticated];
    }

    if (routeLocation.startsWith('/add-goal')) {
      return defaultPermissions;
    }

    final IList<String> appRoutes = IListConst([
      // bottom nav stack
      const HomeRoute().location,
      const GoalsRoute().location,
      const MapRoute().location,
      const SummitRoute().location,
      // home nested (full screen)
      const ChatRoute().location,
      const NotificationRoute().location,

      const ProfileRoute().location,
      const EditProfileRoute().location,
      const CompleteProfileRoute().location,
      const FeedbackRoute().location,
      const SubscriptionRoute().location,
      const DeleteAccountRoute().location,
      // onboarding
      const OnboardingAddGoalRoute().location,
      const AddGoalRoute().location,
    ]);

    if (appRoutes.any(routeLocation.startsWith)) {
      return defaultPermissions;
    }

    throw UnimplementedError('Route $uri has not set nav permissions.');
  }
}
