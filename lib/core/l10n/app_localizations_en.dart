// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get english => 'English';

  @override
  String get unauthorizedError => 'Unauthorized';

  @override
  String get forbiddenError => 'Forbidden';

  @override
  String get notFoundError => 'Not Found';

  @override
  String get conflictError => 'Conflict';

  @override
  String get internalError => 'Something went wrong. Please try again later.';

  @override
  String get serviceUnavailableError => 'Service Unavailable';

  @override
  String get timeoutError => 'Timeout';

  @override
  String get noInternetError => 'No Internet Connection';

  @override
  String get unknownError => 'Something went wrong. Please try again.';

  @override
  String get returnToHome => 'Return to Home Page';

  @override
  String get signInButton => 'Sign In';

  @override
  String get signUpButton => 'Sign Up';

  @override
  String get landingScreenTitle => 'Sign in to continue';

  @override
  String get landingScreenSubtitle => 'Let\'s continue your brain health\njourney with NEURO World!';

  @override
  String get landingSSOApple => 'Sign In with Apple';

  @override
  String get landingSSOGoogle => 'Sign In with Google';

  @override
  String get landingSSOEmail => 'Sign In with Email';

  @override
  String get landingScreenDividerText => 'OR';

  @override
  String get landingScreenButton => 'Start a New Journey';

  @override
  String get loginScreenTitle => 'Welcome Back, NEURO Navigator!';

  @override
  String get loginScreenSubtitle => 'Ready to power up with NEURO World? Let\'s do this!';

  @override
  String get loginEmailPlaceholder => 'Enter your email address';

  @override
  String get loginPasswordPlaceholder => 'Enter your password';

  @override
  String get loginForgotPassword => 'Forgot Password?';

  @override
  String get signupScreenTitle => 'Welcome to NEURO World!';

  @override
  String get signupScreenSubTitle => 'Unlock your journey to NEURO World using email and password';

  @override
  String get signupFullNamePlaceholder => 'Enter your full name';

  @override
  String get signupEmailPlaceholder => 'Enter your email address';

  @override
  String get signupPasswordPlaceholder => 'Enter password';

  @override
  String get fullNameFieldLabel => 'Full Name';

  @override
  String get emailFieldLabel => 'Email';

  @override
  String get passwordFieldLabel => 'Password';

  @override
  String get validationNameRequired => 'Name is required';

  @override
  String get validationNamePattern => 'Name cannot contain any numbers or symbols';

  @override
  String get validationEmailPattern => 'Enter a valid email address';

  @override
  String get validationEmailRequired => 'Email is required';

  @override
  String get validationPasswordRequired => 'Password is required';

  @override
  String get validationPasswordLength => 'Password must be at least 8 characters long';

  @override
  String get validationTncCheckboxRequired => 'You must agree to the terms & conditions';

  @override
  String get tncCheckboxiAgree => 'I agree to the ';

  @override
  String get tncCheckboxLinkText => 'Terms & Conditions';

  @override
  String get forgotPasswordScreenTitle => 'Forgot password?';

  @override
  String get forgotPasswordScreenSubtitle => 'No worries. Let\'s get you back in!';

  @override
  String get submitButton => 'Submit';

  @override
  String get backToLoginButton => 'Back to login';

  @override
  String get termsAndConditionsScreenTitle => 'Terms and Conditions';

  @override
  String get ssoLoading => 'Signing in...';

  @override
  String get logoutLoading => 'Signing out ...';

  @override
  String get ssoErrorCanceled => 'The sign in process was canceled';

  @override
  String get googleSSOError => 'An error occurred during Sign in with Google';

  @override
  String get appleSSOErrorUnknown => 'An error occurred during Sign in with Apple';

  @override
  String get errorDialogTitle => 'Error';

  @override
  String get errorDialogButton => 'Close';

  @override
  String get homeTab => 'Home';

  @override
  String get goalsTab => 'Goals';

  @override
  String get mapTab => 'Map';

  @override
  String get summitTab => 'Summit';

  @override
  String get mylaTooltip => 'Interact with Myla';

  @override
  String get chatScreenTitle => 'Interact with Myla';

  @override
  String get chatMylaGreeting => 'Hello, what can I help\nyou with?';

  @override
  String get chatExamplePrompt1 => 'Guide me to healthier eating habits';

  @override
  String get chatExamplePrompt2 => 'Create a personalized workout plan for me';

  @override
  String get chatExamplePrompt3 => 'Strategies to optimize energy levels';

  @override
  String get chatDisclaimerNote => 'NOTE: ';

  @override
  String get chatDisclaimer => 'MYLA is an AI assistant and cannot provide medical advice. Please consult a healthcare professional for medical concerns.';

  @override
  String get chatLearnMore => 'Learn more';

  @override
  String get chatConsentTitle => 'AI Bot Consent';

  @override
  String get chatConsentBody => 'Our AI bot offers general brain health guidance and does not provide medical advice, diagnoses, or prescriptions. The application is not liable for actions taken based on its responses. Consult a healthcare professional for medical concerns. By proceeding, you agree to these terms.';

  @override
  String get chatConsentAgree => 'I Agree';

  @override
  String get chatConsentDecline => 'Decline';

  @override
  String get chatInputPlaceholder => 'Ask anything here...';

  @override
  String get chatErrorGeneric => 'An unexpected error occurred while prompting Myla. Please try again.';

  @override
  String get chatCTAButton => 'Check out your new goal';

  @override
  String get goalsTabTitle => 'NEURO Goals';

  @override
  String get goalsTabAction => 'Add';

  @override
  String get goalUnmarked => 'Check in to earn more NPs!';

  @override
  String get goalIncomplete => 'Meet your goal';

  @override
  String get goalComplete => 'Weekly goal completed';

  @override
  String get checkInSlider => 'Slide to Check In';

  @override
  String get checkInSuccessButton => 'Done';

  @override
  String get repairStreakSlider => 'Repair your streak';

  @override
  String get repairStreaksCardTitle => 'Save your streaks!';

  @override
  String get repairStreaksCardBody => 'Repair your streak now';

  @override
  String get repairStreakSheetTitle => 'Repair your streak!';

  @override
  String get repairStreakSheetSubtitle => 'You\'re just one step away from reviving your streak. Repair it and grow stronger!';

  @override
  String get repairStreakSheetNotEnoughPoints => 'Oops! You don\'t have enough Neuro Points right now. But no worries, there is still time to earn more and bring your streak back!';

  @override
  String get repairStreakTooltip1 => 'Repair your streak now with ';

  @override
  String get repairStreakTooltip2 => ' and continue your progress from where you left.';

  @override
  String get repairStreakTooltipNotEnoughPoints => 'Complete today\'s check-ins to earn Neuro Points and get your streak back on track!';

  @override
  String get repairStreakSheetRepairButton => 'Repair with';

  @override
  String get repairStreakSheetStartFreshButton => 'Start Fresh';

  @override
  String get repairStreakSuccessDialogTitle => 'Streak repaired!';

  @override
  String get repairStreakSuccessDialogBody1 => 'You\'re back in the game! Your goal streak has been restored.';

  @override
  String get repairStreakSuccessDialogBody2 => 'Continue where you left off!';

  @override
  String get repairStreakSuccessDialogButton => 'Back to Goals';

  @override
  String get startFreshConfirmationTitle => 'Are you sure you want to\nstart fresh?';

  @override
  String get startFreshConfirmationBody => 'This will start your journey again from Week 1. You will not be able to recover your previous streak.';

  @override
  String get startFreshConfirmationAccept => 'Yes';

  @override
  String get startFreshConfirmationCancel => 'Go Back';

  @override
  String get saveYourStreaksTitle => 'Save your streaks!';

  @override
  String get saveYourStreaksSubtitle => 'Didn\'t reach your weekly goal? No worries. Let\'s keep it going!';

  @override
  String get saveYourStreaksTooltip => 'Restore your streaks using NPs and continue your progress from where you left.';

  @override
  String get saveYourStreaksButton => 'Back to Goals';

  @override
  String get menuButtonTooltip => 'Open Menu';

  @override
  String get menuTitle => 'NEURO World';

  @override
  String get menuItemProfile => 'Profile';

  @override
  String get menuItemSubscription => 'Subscription Plan';

  @override
  String get menuItemFeedback => 'Feedback';

  @override
  String get menuItemDeleteAccount => 'Delete Account';

  @override
  String get menuItemLogout => 'Logout';

  @override
  String get streaksAchievementLabel => 'Active Streaks';

  @override
  String get badgesAchievementLabel => 'Total Badges';

  @override
  String get pointsAchievementLabel => 'NEURO Points';

  @override
  String get homeTabCardAction => 'Tap to view';

  @override
  String get todaysGoalProgressCardLabel => 'Goals Progress';

  @override
  String get todaysGoalProgressCardIncompleteLabel => 'Don\'t forget to check-in on your goals.';

  @override
  String get todaysGoalProgressCardCompleteLabel => 'Nice work! All goals are completed for today.';

  @override
  String get restoreStreakCardLastDaySingle => 'You\'re close to breaking a streak. Check-in to save it!';

  @override
  String get restoreStreakCardLastDayMultiple => 'goals are at risk of losing their streaks. Tap to act now!';

  @override
  String get restoreStreakCardBrokenStreaks1 => 'Only';

  @override
  String get restoreStreakCardBrokenStreaks2Single => 'day left to repair your broken streak! Tap now before it\'s lost.';

  @override
  String get restoreStreakCardBrokenStreaks2Multiple => 'days left to repair your broken streaks! Tap now before it\'s lost.';

  @override
  String get neuroCityCardLabel => 'NEURO City';

  @override
  String get neuroCityCardText => 'Your Neuropolis city is evolving. Check it out! 🎉';

  @override
  String get currentPlanCardLabel => 'Current Plan';

  @override
  String get currentPlanCardText => 'Unlock Premium: Get personalized insights and exclusive features—upgrade now for the ultimate experience!';

  @override
  String get goalDetailMylaTooltip => 'Here is everything you need to know about';

  @override
  String get goalsMoreEditOption => 'Edit Goal';

  @override
  String get goalsMoreDetailsOption => 'Goal Details';

  @override
  String get editGoalSheetTitle => 'Edit Goal';

  @override
  String get editGoalSheetSubtitle => 'Change the frequency of your current goal to the desired level.';

  @override
  String get nutritionGoal => 'Set your Nutrition goal';

  @override
  String get onboardingStepOne => 'My strong and steady friend who knows that real power starts with the right fuel. He\'s mastered the art of brain-healthy eating and will help you nourish your mind with simple, delicious choices!';

  @override
  String get meetNuri => 'Let\'s meet Nuri';

  @override
  String get nutritionChoice => 'Hi, I\'m Nuri! I\'ll be your guide to making better nutrition choices.';

  @override
  String get strongMind => 'Strong minds need strong food. Pick a few delicious habits!';

  @override
  String get letsGo => 'Sure, Lets go!';

  @override
  String get changeGoal => 'Change Goal';

  @override
  String get changeIsland => 'Change Island';

  @override
  String get changeIntensity => 'Change Intensity';

  @override
  String get setIntensity => 'Lets set the intensity of your goal.';

  @override
  String get confirmDays => 'Confirm Days';

  @override
  String get greatJob => 'Great job! we are almost there. now lets set the days for this goal and are good to go.';

  @override
  String get congratulationsUnlock => 'Congratulations! You have unlocked a new land by setting up this goal.';

  @override
  String get explore => 'Now let\'s explore the vibrant land you\'ve just unlocked.';

  @override
  String get exploreCity => 'Explore the city';

  @override
  String get next => 'Next';

  @override
  String get gettingGoal => 'Getting your goals';

  @override
  String get settingGoal => 'Setting your goal';

  @override
  String get selectIsland => 'Select island';

  @override
  String get askMe => 'Ask me anything';

  @override
  String get letMeKnow => 'Let me know if you need help picking up a new habit';

  @override
  String get emptyGoalsTitle => 'No Goals Available';

  @override
  String get emptyGoalsText => 'Oops! You have not selected any goals. \nTap the button below to get started.';

  @override
  String get noTrackingFound => 'Oops! You didn\'t track any goals on this date. \nSelect a future date to see your goals.';

  @override
  String get emptyGoalsAction => 'Add Goal';

  @override
  String get goalsDescriptionEmpty => 'This goal does not have a description yet.';

  @override
  String get editGoalLoading => 'Updating goal...';

  @override
  String get awardDaily => 'Daily Check-in';

  @override
  String get awardExtra => 'Extra Check-in';

  @override
  String get awardWeekStreak => 'Week Streak Achieved';

  @override
  String get awardMonthStreak => 'Month Streak Achieved';

  @override
  String get award3MonthStreak => '3 Months Streak Achieved';

  @override
  String get award6MonthStreak => '6 Months Streak Achieved';

  @override
  String get goalCardUnstarted => 'Check-in to start your goal.';

  @override
  String get goalCardFinalChance => 'Final chance to save your streak!';

  @override
  String get goalCardNoCheckin => 'Check-in now and stay on track.';

  @override
  String get goalCardOneCheckin => 'Great start! Keep going.';

  @override
  String get goalCardOneAway => 'You\'re just one check-in away!';

  @override
  String get goalCardTwoAway => 'You\'re just two check-ins away!';

  @override
  String get goalCardHalfWay => 'You\'re halfway there! Keep it up.';

  @override
  String get goalCardDone => 'Weekly goal done!';

  @override
  String get goalCardExtra => 'Extra check-ins, going beyond!';

  @override
  String get goalCardDefault => 'Meet your goal.';

  @override
  String get comingSoon => 'Coming Soon!';

  @override
  String get nutritionTitle => 'Nutrition';

  @override
  String get nutritionSubtitle => 'Brain-healthy diets and cooking.';

  @override
  String get exerciseTitle => 'Exercise';

  @override
  String get exerciseSubtitle => 'Workouts, sports, and movement.';

  @override
  String get unwindTitle => 'Unwind';

  @override
  String get unwindSubtitle => 'Stress management and meditation.';

  @override
  String get restoreTitle => 'Restore';

  @override
  String get restoreSubtitle => 'Consistent good-quality sleep.';

  @override
  String get optimizeTitle => 'Optimize';

  @override
  String get optimizeSubtitle => 'Activities, hobbies, and socialising.';

  @override
  String get listView => 'List View';

  @override
  String get mapView => 'Map View';

  @override
  String get profileScreenTitle => 'Profile';

  @override
  String get profileEmail => 'Email';

  @override
  String get profileName => 'Name';

  @override
  String get profileDoB => 'Date of Birth';

  @override
  String get profileDoBField => 'Select Date of Birth';

  @override
  String get profileGender => 'Gender';

  @override
  String get profileGenderField => 'Select Gender';

  @override
  String get profileNameHint => 'Enter your name';

  @override
  String get profileDoBHint => 'Select a date';

  @override
  String get profileGenderHint => 'Select gender';

  @override
  String get completeProfileCardTitle => 'Complete Your Profile';

  @override
  String get completeProfileCardBody => 'Complete your profile to unlock a more tailored and personalized experience.';

  @override
  String get completeProfileScreenTitle => 'Complete Profile';

  @override
  String get completeProfileScreenButton => 'Complete Profile';

  @override
  String get editProfileScreenTitle => 'Edit Profile';

  @override
  String get editProfileScreenButton => 'Save Changes';

  @override
  String get editProfileScreenAddPicture => 'Add Picture';

  @override
  String get editProfileScreenUpdatePicture => 'Update Picture';

  @override
  String get editProfilePictureCamera => 'Take Photo';

  @override
  String get editProfilePictureGallery => 'Choose from Gallery';

  @override
  String get updateProfilePicureLoadingText => 'Updating Picture...';

  @override
  String get updateProfileLoadingText => 'Saving Changes...';

  @override
  String get onboardingStepOneTitle => 'Welcome to\nNEURO World!';

  @override
  String get onboardingStepTwoTitle => 'Getting Started\nwith Myla';

  @override
  String get onboardingStepOneDescription => 'Your journey to a healthier, sharper, and more resilient mind starts now. Myla, your guide, will support you every step of the way. Let\'s build habits that transform your brain and body!';

  @override
  String get onboardingStepTwoDescription => 'Hi there! I\'m Myla, the brain of NEURO World. I\'ll be your guide as you unlock islands of Nutrition, Exercise, Stress Management, Restorative Sleep, and Cognitive Optimization. Ready to begin? Let\'s go!';

  @override
  String get onboardingStepThreeDescription => 'What kind of habit do you want to build? Tap the island we\'ll go to next!';

  @override
  String get stepOneButton => 'Continue';

  @override
  String get letsGetStarted => 'Let\'s Get Started';

  @override
  String get confirmationDialogAcceptButton => 'OK';

  @override
  String get confirmationDialogDeclineButton => 'Cancel';

  @override
  String get feedbackScreenTitle => 'Feedback';

  @override
  String get feedbackScreenSubtitle => 'How likely are you to recommend us to a friend or colleagues?';

  @override
  String get feedbackScreenSubtitle2 => 'How would you share your experience with us?';

  @override
  String get feedbackButtonText => 'Submit Feedback';

  @override
  String get feedbackShareExperience => 'Share your experience here...';

  @override
  String get submittingFeedbackLoadingText => 'Submitting Feedback...';

  @override
  String get nameLengthValidation => 'Name must be less than 30 characters';

  @override
  String get validationPasswordMaxLength => 'Password must be less than 50 characters';

  @override
  String get validationEmailMaxLength => 'Email must be less than 50 characters';

  @override
  String get feedbackSubtitle => 'How likely are you to recommend us to a friend or colleague?';

  @override
  String get notificationTitle => 'Notifications';

  @override
  String get notificationNewLabel => 'New';

  @override
  String get notificationEarlierLabel => 'Earlier';

  @override
  String get notificationMarkRead => 'Mark as all read';

  @override
  String get notificationPermissionDialogTitle => 'Enable alerts';

  @override
  String get notificationPermissionDialogDescription => 'Enable alerts to keep track of your goals and streaks!';

  @override
  String get notificationPermissionDialogAcept => 'Yes, I\'m in!';

  @override
  String get notificationPermissionDialogLater => 'Later';

  @override
  String get deleteAccountScreenTitle => 'Delete Account';

  @override
  String get deleteAccountConfirmationTitle => 'Delete Account Confirmation';

  @override
  String get deleteAccountConfirmationSubtitle => 'Are you sure you want to permanently delete your account? This action cannot be undone, and all your data and profile information will be lost.';

  @override
  String get deleteAccountButtonTitle => 'Delete my account';

  @override
  String get deleteAccountSuccessButton => 'Okay';

  @override
  String get deleteAccountConfirmTitle => 'Account Deletion Confirmation';

  @override
  String get deleteAccountConfirmSubtitle => 'Your account has been successfully deleted. All your personal data has been securely removed from our systems. Thank you for using our services.';

  @override
  String get deleteAccountLoading => 'Deleting Account...';

  @override
  String get signInPrefix => 'By tapping Sign In or logging into an existing ';

  @override
  String get signInMiddle => ' account, you agree to our\n';

  @override
  String get termsLink => 'Terms & Conditions';

  @override
  String get signInBeforePrivacy => ' and acknowledge that you have read our ';

  @override
  String get privacyLink => 'Privacy Policy.';

  @override
  String get landingSignupScreenTitle => 'Create new account';

  @override
  String get landingSignupScreenSubtitle => 'Let\'s begin your brain health journey\nwith NEURO World!';

  @override
  String get landingSignupSSOApple => 'Continue with Apple';

  @override
  String get landingSignupSSOGoogle => 'Continue with Google';

  @override
  String get landingSignupSSOEmail => 'Continue with Email';

  @override
  String get privacyPolicyTitle => 'Privacy Policy';

  @override
  String get resetPasswordScreenTitle => 'Reset Password';

  @override
  String get resetPasswordScreenSubTitle => 'Let\'s reset and get you back on your journey!';

  @override
  String get newPassword => 'New Password';

  @override
  String get newPasswordPlaceholder => 'Enter New Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get confirmPasswordPlaceholder => 'Enter Confirm Password';

  @override
  String get subscriptionPlans => 'Subscription Plans';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get freeTier => 'Free Tier';

  @override
  String get habitsTier => 'Habits Tier';

  @override
  String get summitTier => 'Summit Tier';

  @override
  String get clinicalTier => 'Clinical Tier';

  @override
  String get subscriptionPlanTitle => 'Subscription Plan';

  @override
  String get subscriptionPlanFeatures => 'Features';

  @override
  String get subscriptionPlanPrice => 'Price';

  @override
  String get subscriptionPlanSelect => 'Select';

  @override
  String get subscriptionPlanSelected => 'Selected';

  @override
  String get subscriptionPlanMonthly => 'Monthly';

  @override
  String get upgrade => 'Upgrade';

  @override
  String get perMonth => 'Per Month';

  @override
  String get perYear => 'Per Year';
}
