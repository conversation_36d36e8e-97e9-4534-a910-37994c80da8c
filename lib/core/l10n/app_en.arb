{"@@locale": "en", "english": "English", "unauthorizedError": "Unauthorized", "forbiddenError": "Forbidden", "notFoundError": "Not Found", "conflictError": "Conflict", "internalError": "Something went wrong. Please try again later.", "serviceUnavailableError": "Service Unavailable", "timeoutError": "Timeout", "noInternetError": "No Internet Connection", "unknownError": "Something went wrong. Please try again.", "returnToHome": "Return to Home Page", "signInButton": "Sign In", "signUpButton": "Sign Up", "landingScreenTitle": "Sign in to continue", "landingScreenSubtitle": "Let's continue your brain health\njourney with NEURO World!", "landingSSOApple": "Sign In with Apple", "landingSSOGoogle": "Sign In with Google", "landingSSOEmail": "Sign In with <PERSON><PERSON>", "landingScreenDividerText": "OR", "landingScreenButton": "Start a New Journey", "loginScreenTitle": "Welcome Back, NEURO Navigator!", "loginScreenSubtitle": "Ready to power up with NEURO World? Let's do this!", "loginEmailPlaceholder": "Enter your email address", "loginPasswordPlaceholder": "Enter your password", "loginForgotPassword": "Forgot Password?", "signupScreenTitle": "Welcome to NEURO World!", "signupScreenSubTitle": "Unlock your journey to NEURO World using email and password", "signupFullNamePlaceholder": "Enter your full name", "signupEmailPlaceholder": "Enter your email address", "signupPasswordPlaceholder": "Enter password", "fullNameFieldLabel": "Full Name", "emailFieldLabel": "Email", "passwordFieldLabel": "Password", "validationNameRequired": "Name is required", "validationNamePattern": "Name cannot contain any numbers or symbols", "validationEmailPattern": "Enter a valid email address", "validationEmailRequired": "Email is required", "validationPasswordRequired": "Password is required", "validationPasswordLength": "Password must be at least 8 characters long", "validationTncCheckboxRequired": "You must agree to the terms & conditions", "tncCheckboxiAgree": "I agree to the ", "tncCheckboxLinkText": "Terms & Conditions", "forgotPasswordScreenTitle": "Forgot password?", "forgotPasswordScreenSubtitle": "No worries. Let's get you back in!", "submitButton": "Submit", "backToLoginButton": "Back to login", "termsAndConditionsScreenTitle": "Terms and Conditions", "ssoLoading": "Signing in...", "logoutLoading": "Signing out ...", "ssoErrorCanceled": "The sign in process was canceled", "googleSSOError": "An error occurred during Sign in with Google", "appleSSOErrorUnknown": "An error occurred during Sign in with Apple", "errorDialogTitle": "Error", "errorDialogButton": "Close", "homeTab": "Home", "goalsTab": "Goals", "mapTab": "Map", "summitTab": "Summit", "mylaTooltip": "Interact with <PERSON><PERSON>", "chatScreenTitle": "Interact with <PERSON><PERSON>", "chatMylaGreeting": "Hello, what can I help\nyou with?", "chatExamplePrompt1": "Guide me to healthier eating habits", "chatExamplePrompt2": "Create a personalized workout plan for me", "chatExamplePrompt3": "Strategies to optimize energy levels", "chatDisclaimerNote": "NOTE: ", "chatDisclaimer": "<PERSON><PERSON><PERSON> is an AI assistant and cannot provide medical advice. Please consult a healthcare professional for medical concerns.", "chatLearnMore": "Learn more", "chatConsentTitle": "AI <PERSON><PERSON>", "chatConsentBody": "Our AI bot offers general brain health guidance and does not provide medical advice, diagnoses, or prescriptions. The application is not liable for actions taken based on its responses. Consult a healthcare professional for medical concerns. By proceeding, you agree to these terms.", "chatConsentAgree": "I Agree", "chatConsentDecline": "Decline", "chatInputPlaceholder": "Ask anything here...", "chatErrorGeneric": "An unexpected error occurred while prompting <PERSON><PERSON>. Please try again.", "chatCTAButton": "Check out your new goal", "goalsTabTitle": "NEURO Goals", "goalsTabAction": "Add", "goalUnmarked": "Check in to earn more NPs!", "goalIncomplete": "Meet your goal", "goalComplete": "Weekly goal completed", "checkInSlider": "Slide to Check In", "checkInSuccessButton": "Done", "repairStreakSlider": "Repair your streak", "repairStreaksCardTitle": "Save your streaks!", "repairStreaksCardBody": "Repair your streak now", "repairStreakSheetTitle": "Repair your streak!", "repairStreakSheetSubtitle": "You're just one step away from reviving your streak. Repair it and grow stronger!", "repairStreakSheetNotEnoughPoints": "Oops! You don't have enough Neuro Points right now. But no worries, there is still time to earn more and bring your streak back!", "repairStreakTooltip1": "Repair your streak now with ", "repairStreakTooltip2": " and continue your progress from where you left.", "repairStreakTooltipNotEnoughPoints": "Complete today's check-ins to earn Neuro Points and get your streak back on track!", "repairStreakSheetRepairButton": "Repair with", "repairStreakSheetStartFreshButton": "Start Fresh", "repairStreakSuccessDialogTitle": "Streak repaired!", "repairStreakSuccessDialogBody1": "You're back in the game! Your goal streak has been restored.", "repairStreakSuccessDialogBody2": "Continue where you left off!", "repairStreakSuccessDialogButton": "Back to Goals", "startFreshConfirmationTitle": "Are you sure you want to\nstart fresh?", "startFreshConfirmationBody": "This will start your journey again from Week 1. You will not be able to recover your previous streak.", "startFreshConfirmationAccept": "Yes", "startFreshConfirmationCancel": "Go Back", "saveYourStreaksTitle": "Save your streaks!", "saveYourStreaksSubtitle": "Didn't reach your weekly goal? No worries. Let's keep it going!", "saveYourStreaksTooltip": "Restore your streaks using NPs and continue your progress from where you left.", "saveYourStreaksButton": "Back to Goals", "menuButtonTooltip": "Open Menu", "menuTitle": "NEURO World", "menuItemProfile": "Profile", "menuItemSubscription": "Subscription Plan", "menuItemFeedback": "<PERSON><PERSON><PERSON>", "menuItemDeleteAccount": "Delete Account", "menuItemLogout": "Logout", "streaksAchievementLabel": "Active Streaks", "badgesAchievementLabel": "Total Badges", "pointsAchievementLabel": "NEURO Points", "homeTabCardAction": "Tap to view", "todaysGoalProgressCardLabel": "Goals Progress", "todaysGoalProgressCardIncompleteLabel": "Don't forget to check-in on your goals.", "todaysGoalProgressCardCompleteLabel": "Nice work! All goals are completed for today.", "restoreStreakCardLastDaySingle": "You're close to breaking a streak. Check-in to save it!", "restoreStreakCardLastDayMultiple": "goals are at risk of losing their streaks. Tap to act now!", "restoreStreakCardBrokenStreaks1": "Only", "restoreStreakCardBrokenStreaks2Single": "day left to repair your broken streak! Tap now before it's lost.", "restoreStreakCardBrokenStreaks2Multiple": "days left to repair your broken streaks! Tap now before it's lost.", "neuroCityCardLabel": "NEURO City", "neuroCityCardText": "Your Neuropolis city is evolving. Check it out! 🎉", "currentPlanCardLabel": "Current Plan", "currentPlanCardText": "Unlock Premium: Get personalized insights and exclusive features—upgrade now for the ultimate experience!", "goalDetailMylaTooltip": "Here is everything you need to know about", "goalsMoreEditOption": "Edit Goal", "goalsMoreDetailsOption": "Goal Details", "editGoalSheetTitle": "Edit Goal", "editGoalSheetSubtitle": "Change the frequency of your current goal to the desired level.", "nutritionGoal": "Set your Nutrition goal", "onboardingStepOne": "My strong and steady friend who knows that real power starts with the right fuel. He's mastered the art of brain-healthy eating and will help you nourish your mind with simple, delicious choices!", "meetNuri": "Let's meet <PERSON><PERSON>", "nutritionChoice": "Hi, I'm <PERSON><PERSON>! I'll be your guide to making better nutrition choices.", "strongMind": "Strong minds need strong food. Pick a few delicious habits!", "letsGo": "Sure, Lets go!", "changeGoal": "Change Goal", "changeIsland": "Change Island", "changeIntensity": "Change Intensity", "setIntensity": "Lets set the intensity of your goal.", "confirmDays": "Confirm Days", "greatJob": "Great job! we are almost there. now lets set the days for this goal and are good to go.", "congratulationsUnlock": "Congratulations! You have unlocked a new land by setting up this goal.", "explore": "Now let's explore the vibrant land you've just unlocked.", "exploreCity": "Explore the city", "next": "Next", "gettingGoal": "Getting your goals", "settingGoal": "Setting your goal", "selectIsland": "Select island", "askMe": "Ask me anything", "letMeKnow": "Let me know if you need help picking up a new habit", "emptyGoalsTitle": "No Goals Available", "emptyGoalsText": "Oops! You have not selected any goals. \nTap the button below to get started.", "noTrackingFound": "Oops! You didn't track any goals on this date. \nSelect a future date to see your goals.", "emptyGoalsAction": "Add Goal", "goalsDescriptionEmpty": "This goal does not have a description yet.", "editGoalLoading": "Updating goal...", "awardDaily": "Daily Check-in", "awardExtra": "Extra Check-in", "awardWeekStreak": "Week Streak Achieved", "awardMonthStreak": "Month Streak Achieved", "award3MonthStreak": "3 Months Streak Achieved", "award6MonthStreak": "6 Months Streak Achieved", "goalCardUnstarted": "Check-in to start your goal.", "goalCardFinalChance": "Final chance to save your streak!", "goalCardNoCheckin": "Check-in now and stay on track.", "goalCardOneCheckin": "Great start! Keep going.", "goalCardOneAway": "You're just one check-in away!", "goalCardTwoAway": "You're just two check-ins away!", "goalCardHalfWay": "You're halfway there! Keep it up.", "goalCardDone": "Weekly goal done!", "goalCardExtra": "Extra check-ins, going beyond!", "goalCardDefault": "Meet your goal.", "comingSoon": "Coming Soon!", "nutritionTitle": "Nutrition", "nutritionSubtitle": "Brain-healthy diets and cooking.", "exerciseTitle": "Exercise", "exerciseSubtitle": "Workouts, sports, and movement.", "unwindTitle": "Unwind", "unwindSubtitle": "Stress management and meditation.", "restoreTitle": "Rest<PERSON>", "restoreSubtitle": "Consistent good-quality sleep.", "optimizeTitle": "Optimize", "optimizeSubtitle": "Activities, hobbies, and socialising.", "listView": "List View", "mapView": "Map View", "profileScreenTitle": "Profile", "profileEmail": "Email", "profileName": "Name", "profileDoB": "Date of Birth", "profileDoBField": "Select Date of Birth", "profileGender": "Gender", "profileGenderField": "Select Gender", "profileNameHint": "Enter your name", "profileDoBHint": "Select a date", "profileGenderHint": "Select gender", "completeProfileCardTitle": "Complete Your Profile", "completeProfileCardBody": "Complete your profile to unlock a more tailored and personalized experience.", "completeProfileScreenTitle": "Complete Profile", "completeProfileScreenButton": "Complete Profile", "editProfileScreenTitle": "Edit Profile", "editProfileScreenButton": "Save Changes", "editProfileScreenAddPicture": "Add Picture", "editProfileScreenUpdatePicture": "Update Picture", "editProfilePictureCamera": "Take Photo", "editProfilePictureGallery": "Choose from Gallery", "updateProfilePicureLoadingText": "Updating Picture...", "updateProfileLoadingText": "Saving Changes...", "onboardingStepOneTitle": "Welcome to\nNEURO World!", "onboardingStepTwoTitle": "Getting Started\nwith <PERSON><PERSON>", "onboardingStepOneDescription": "Your journey to a healthier, sharper, and more resilient mind starts now. <PERSON><PERSON>, your guide, will support you every step of the way. Let's build habits that transform your brain and body!", "onboardingStepTwoDescription": "Hi there! I'm <PERSON><PERSON>, the brain of NEURO World. I'll be your guide as you unlock islands of Nutrition, Exercise, Stress Management, Restorative Sleep, and Cognitive Optimization. Ready to begin? Let's go!", "onboardingStepThreeDescription": "What kind of habit do you want to build? Tap the island we'll go to next!", "stepOneButton": "Continue", "letsGetStarted": "Let's Get Started", "confirmationDialogAcceptButton": "OK", "confirmationDialogDeclineButton": "Cancel", "feedbackScreenTitle": "<PERSON><PERSON><PERSON>", "feedbackScreenSubtitle": "How likely are you to recommend us to a friend or colleagues?", "feedbackScreenSubtitle2": "How would you share your experience with us?", "feedbackButtonText": "Submit <PERSON>", "feedbackShareExperience": "Share your experience here...", "submittingFeedbackLoadingText": "Submitting Feedback...", "nameLengthValidation": "Name must be less than 30 characters", "validationPasswordMaxLength": "Password must be less than 50 characters", "validationEmailMaxLength": "Email must be less than 50 characters", "feedbackSubtitle": "How likely are you to recommend us to a friend or colleague?", "notificationTitle": "Notifications", "notificationNewLabel": "New", "notificationEarlierLabel": "Earlier", "notificationMarkRead": "<PERSON> as all read", "notificationPermissionDialogTitle": "Enable alerts", "notificationPermissionDialogDescription": "Enable alerts to keep track of your goals and streaks!", "notificationPermissionDialogAcept": "Yes, I'm in!", "notificationPermissionDialogLater": "Later", "deleteAccountScreenTitle": "Delete Account", "deleteAccountConfirmationTitle": "Delete Account Confirmation", "deleteAccountConfirmationSubtitle": "Are you sure you want to permanently delete your account? This action cannot be undone, and all your data and profile information will be lost.", "deleteAccountButtonTitle": "Delete my account", "deleteAccountSuccessButton": "Okay", "deleteAccountConfirmTitle": "Account Deletion Confirmation", "deleteAccountConfirmSubtitle": "Your account has been successfully deleted. All your personal data has been securely removed from our systems. Thank you for using our services.", "deleteAccountLoading": "Deleting Account...", "signInPrefix": "By tapping Sign In or logging into an existing ", "signInMiddle": " account, you agree to our\n", "termsLink": "Terms & Conditions", "signInBeforePrivacy": " and acknowledge that you have read our ", "privacyLink": "Privacy Policy.", "landingSignupScreenTitle": "Create new account", "landingSignupScreenSubtitle": "Let's begin your brain health journey\nwith NEURO World!", "landingSignupSSOApple": "Continue with Apple", "landingSignupSSOGoogle": "Continue with Google", "landingSignupSSOEmail": "Continue with <PERSON>ail", "privacyPolicyTitle": "Privacy Policy", "resetPasswordScreenTitle": "Reset Password", "resetPasswordScreenSubTitle": "Let's reset and get you back on your journey!", "newPassword": "New Password", "newPasswordPlaceholder": "Enter New Password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Enter Confirm Password", "subscriptionPlans": "Subscription Plans", "monthly": "Monthly", "yearly": "Yearly", "freeTier": "Free Tier", "habitsTier": "Habits Tier", "summitTier": "Summit Tier", "clinicalTier": "Clinical Tier", "subscriptionPlanTitle": "Subscription Plan", "subscriptionPlanFeatures": "Features", "subscriptionPlanPrice": "Price", "subscriptionPlanSelect": "Select", "subscriptionPlanSelected": "Selected", "subscriptionPlanMonthly": "Monthly", "upgrade": "Upgrade", "perMonth": "Per Month", "perYear": "Per Year"}