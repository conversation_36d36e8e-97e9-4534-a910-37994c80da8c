import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en')
  ];

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @unauthorizedError.
  ///
  /// In en, this message translates to:
  /// **'Unauthorized'**
  String get unauthorizedError;

  /// No description provided for @forbiddenError.
  ///
  /// In en, this message translates to:
  /// **'Forbidden'**
  String get forbiddenError;

  /// No description provided for @notFoundError.
  ///
  /// In en, this message translates to:
  /// **'Not Found'**
  String get notFoundError;

  /// No description provided for @conflictError.
  ///
  /// In en, this message translates to:
  /// **'Conflict'**
  String get conflictError;

  /// No description provided for @internalError.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong. Please try again later.'**
  String get internalError;

  /// No description provided for @serviceUnavailableError.
  ///
  /// In en, this message translates to:
  /// **'Service Unavailable'**
  String get serviceUnavailableError;

  /// No description provided for @timeoutError.
  ///
  /// In en, this message translates to:
  /// **'Timeout'**
  String get timeoutError;

  /// No description provided for @noInternetError.
  ///
  /// In en, this message translates to:
  /// **'No Internet Connection'**
  String get noInternetError;

  /// No description provided for @unknownError.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong. Please try again.'**
  String get unknownError;

  /// No description provided for @returnToHome.
  ///
  /// In en, this message translates to:
  /// **'Return to Home Page'**
  String get returnToHome;

  /// No description provided for @signInButton.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signInButton;

  /// No description provided for @signUpButton.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpButton;

  /// No description provided for @landingScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign in to continue'**
  String get landingScreenTitle;

  /// No description provided for @landingScreenSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Let\'s continue your brain health\njourney with NEURO World!'**
  String get landingScreenSubtitle;

  /// No description provided for @landingSSOApple.
  ///
  /// In en, this message translates to:
  /// **'Sign In with Apple'**
  String get landingSSOApple;

  /// No description provided for @landingSSOGoogle.
  ///
  /// In en, this message translates to:
  /// **'Sign In with Google'**
  String get landingSSOGoogle;

  /// No description provided for @landingSSOEmail.
  ///
  /// In en, this message translates to:
  /// **'Sign In with Email'**
  String get landingSSOEmail;

  /// No description provided for @landingScreenDividerText.
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get landingScreenDividerText;

  /// No description provided for @landingScreenButton.
  ///
  /// In en, this message translates to:
  /// **'Start a New Journey'**
  String get landingScreenButton;

  /// No description provided for @loginScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Welcome Back, NEURO Navigator!'**
  String get loginScreenTitle;

  /// No description provided for @loginScreenSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Ready to power up with NEURO World? Let\'s do this!'**
  String get loginScreenSubtitle;

  /// No description provided for @loginEmailPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get loginEmailPlaceholder;

  /// No description provided for @loginPasswordPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter your password'**
  String get loginPasswordPlaceholder;

  /// No description provided for @loginForgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get loginForgotPassword;

  /// No description provided for @signupScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Welcome to NEURO World!'**
  String get signupScreenTitle;

  /// No description provided for @signupScreenSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Unlock your journey to NEURO World using email and password'**
  String get signupScreenSubTitle;

  /// No description provided for @signupFullNamePlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter your full name'**
  String get signupFullNamePlaceholder;

  /// No description provided for @signupEmailPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get signupEmailPlaceholder;

  /// No description provided for @signupPasswordPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter password'**
  String get signupPasswordPlaceholder;

  /// No description provided for @fullNameFieldLabel.
  ///
  /// In en, this message translates to:
  /// **'Full Name'**
  String get fullNameFieldLabel;

  /// No description provided for @emailFieldLabel.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailFieldLabel;

  /// No description provided for @passwordFieldLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordFieldLabel;

  /// No description provided for @validationNameRequired.
  ///
  /// In en, this message translates to:
  /// **'Name is required'**
  String get validationNameRequired;

  /// No description provided for @validationNamePattern.
  ///
  /// In en, this message translates to:
  /// **'Name cannot contain any numbers or symbols'**
  String get validationNamePattern;

  /// No description provided for @validationEmailPattern.
  ///
  /// In en, this message translates to:
  /// **'Enter a valid email address'**
  String get validationEmailPattern;

  /// No description provided for @validationEmailRequired.
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get validationEmailRequired;

  /// No description provided for @validationPasswordRequired.
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get validationPasswordRequired;

  /// No description provided for @validationPasswordLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 8 characters long'**
  String get validationPasswordLength;

  /// No description provided for @validationTncCheckboxRequired.
  ///
  /// In en, this message translates to:
  /// **'You must agree to the terms & conditions'**
  String get validationTncCheckboxRequired;

  /// No description provided for @tncCheckboxiAgree.
  ///
  /// In en, this message translates to:
  /// **'I agree to the '**
  String get tncCheckboxiAgree;

  /// No description provided for @tncCheckboxLinkText.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get tncCheckboxLinkText;

  /// No description provided for @forgotPasswordScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get forgotPasswordScreenTitle;

  /// No description provided for @forgotPasswordScreenSubtitle.
  ///
  /// In en, this message translates to:
  /// **'No worries. Let\'s get you back in!'**
  String get forgotPasswordScreenSubtitle;

  /// No description provided for @submitButton.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submitButton;

  /// No description provided for @backToLoginButton.
  ///
  /// In en, this message translates to:
  /// **'Back to login'**
  String get backToLoginButton;

  /// No description provided for @termsAndConditionsScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get termsAndConditionsScreenTitle;

  /// No description provided for @ssoLoading.
  ///
  /// In en, this message translates to:
  /// **'Signing in...'**
  String get ssoLoading;

  /// No description provided for @logoutLoading.
  ///
  /// In en, this message translates to:
  /// **'Signing out ...'**
  String get logoutLoading;

  /// No description provided for @ssoErrorCanceled.
  ///
  /// In en, this message translates to:
  /// **'The sign in process was canceled'**
  String get ssoErrorCanceled;

  /// No description provided for @googleSSOError.
  ///
  /// In en, this message translates to:
  /// **'An error occurred during Sign in with Google'**
  String get googleSSOError;

  /// No description provided for @appleSSOErrorUnknown.
  ///
  /// In en, this message translates to:
  /// **'An error occurred during Sign in with Apple'**
  String get appleSSOErrorUnknown;

  /// No description provided for @errorDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get errorDialogTitle;

  /// No description provided for @errorDialogButton.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get errorDialogButton;

  /// No description provided for @homeTab.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get homeTab;

  /// No description provided for @goalsTab.
  ///
  /// In en, this message translates to:
  /// **'Goals'**
  String get goalsTab;

  /// No description provided for @mapTab.
  ///
  /// In en, this message translates to:
  /// **'Map'**
  String get mapTab;

  /// No description provided for @summitTab.
  ///
  /// In en, this message translates to:
  /// **'Summit'**
  String get summitTab;

  /// No description provided for @mylaTooltip.
  ///
  /// In en, this message translates to:
  /// **'Interact with Myla'**
  String get mylaTooltip;

  /// No description provided for @chatScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Interact with Myla'**
  String get chatScreenTitle;

  /// No description provided for @chatMylaGreeting.
  ///
  /// In en, this message translates to:
  /// **'Hello, what can I help\nyou with?'**
  String get chatMylaGreeting;

  /// No description provided for @chatExamplePrompt1.
  ///
  /// In en, this message translates to:
  /// **'Guide me to healthier eating habits'**
  String get chatExamplePrompt1;

  /// No description provided for @chatExamplePrompt2.
  ///
  /// In en, this message translates to:
  /// **'Create a personalized workout plan for me'**
  String get chatExamplePrompt2;

  /// No description provided for @chatExamplePrompt3.
  ///
  /// In en, this message translates to:
  /// **'Strategies to optimize energy levels'**
  String get chatExamplePrompt3;

  /// No description provided for @chatDisclaimerNote.
  ///
  /// In en, this message translates to:
  /// **'NOTE: '**
  String get chatDisclaimerNote;

  /// No description provided for @chatDisclaimer.
  ///
  /// In en, this message translates to:
  /// **'MYLA is an AI assistant and cannot provide medical advice. Please consult a healthcare professional for medical concerns.'**
  String get chatDisclaimer;

  /// No description provided for @chatLearnMore.
  ///
  /// In en, this message translates to:
  /// **'Learn more'**
  String get chatLearnMore;

  /// No description provided for @chatConsentTitle.
  ///
  /// In en, this message translates to:
  /// **'AI Bot Consent'**
  String get chatConsentTitle;

  /// No description provided for @chatConsentBody.
  ///
  /// In en, this message translates to:
  /// **'Our AI bot offers general brain health guidance and does not provide medical advice, diagnoses, or prescriptions. The application is not liable for actions taken based on its responses. Consult a healthcare professional for medical concerns. By proceeding, you agree to these terms.'**
  String get chatConsentBody;

  /// No description provided for @chatConsentAgree.
  ///
  /// In en, this message translates to:
  /// **'I Agree'**
  String get chatConsentAgree;

  /// No description provided for @chatConsentDecline.
  ///
  /// In en, this message translates to:
  /// **'Decline'**
  String get chatConsentDecline;

  /// No description provided for @chatInputPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Ask anything here...'**
  String get chatInputPlaceholder;

  /// No description provided for @chatErrorGeneric.
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred while prompting Myla. Please try again.'**
  String get chatErrorGeneric;

  /// No description provided for @chatCTAButton.
  ///
  /// In en, this message translates to:
  /// **'Check out your new goal'**
  String get chatCTAButton;

  /// No description provided for @goalsTabTitle.
  ///
  /// In en, this message translates to:
  /// **'NEURO Goals'**
  String get goalsTabTitle;

  /// No description provided for @goalsTabAction.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get goalsTabAction;

  /// No description provided for @goalUnmarked.
  ///
  /// In en, this message translates to:
  /// **'Check in to earn more NPs!'**
  String get goalUnmarked;

  /// No description provided for @goalIncomplete.
  ///
  /// In en, this message translates to:
  /// **'Meet your goal'**
  String get goalIncomplete;

  /// No description provided for @goalComplete.
  ///
  /// In en, this message translates to:
  /// **'Weekly goal completed'**
  String get goalComplete;

  /// No description provided for @checkInSlider.
  ///
  /// In en, this message translates to:
  /// **'Slide to Check In'**
  String get checkInSlider;

  /// No description provided for @checkInSuccessButton.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get checkInSuccessButton;

  /// No description provided for @repairStreakSlider.
  ///
  /// In en, this message translates to:
  /// **'Repair your streak'**
  String get repairStreakSlider;

  /// No description provided for @repairStreaksCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Save your streaks!'**
  String get repairStreaksCardTitle;

  /// No description provided for @repairStreaksCardBody.
  ///
  /// In en, this message translates to:
  /// **'Repair your streak now'**
  String get repairStreaksCardBody;

  /// No description provided for @repairStreakSheetTitle.
  ///
  /// In en, this message translates to:
  /// **'Repair your streak!'**
  String get repairStreakSheetTitle;

  /// No description provided for @repairStreakSheetSubtitle.
  ///
  /// In en, this message translates to:
  /// **'You\'re just one step away from reviving your streak. Repair it and grow stronger!'**
  String get repairStreakSheetSubtitle;

  /// No description provided for @repairStreakSheetNotEnoughPoints.
  ///
  /// In en, this message translates to:
  /// **'Oops! You don\'t have enough Neuro Points right now. But no worries, there is still time to earn more and bring your streak back!'**
  String get repairStreakSheetNotEnoughPoints;

  /// No description provided for @repairStreakTooltip1.
  ///
  /// In en, this message translates to:
  /// **'Repair your streak now with '**
  String get repairStreakTooltip1;

  /// No description provided for @repairStreakTooltip2.
  ///
  /// In en, this message translates to:
  /// **' and continue your progress from where you left.'**
  String get repairStreakTooltip2;

  /// No description provided for @repairStreakTooltipNotEnoughPoints.
  ///
  /// In en, this message translates to:
  /// **'Complete today\'s check-ins to earn Neuro Points and get your streak back on track!'**
  String get repairStreakTooltipNotEnoughPoints;

  /// No description provided for @repairStreakSheetRepairButton.
  ///
  /// In en, this message translates to:
  /// **'Repair with'**
  String get repairStreakSheetRepairButton;

  /// No description provided for @repairStreakSheetStartFreshButton.
  ///
  /// In en, this message translates to:
  /// **'Start Fresh'**
  String get repairStreakSheetStartFreshButton;

  /// No description provided for @repairStreakSuccessDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Streak repaired!'**
  String get repairStreakSuccessDialogTitle;

  /// No description provided for @repairStreakSuccessDialogBody1.
  ///
  /// In en, this message translates to:
  /// **'You\'re back in the game! Your goal streak has been restored.'**
  String get repairStreakSuccessDialogBody1;

  /// No description provided for @repairStreakSuccessDialogBody2.
  ///
  /// In en, this message translates to:
  /// **'Continue where you left off!'**
  String get repairStreakSuccessDialogBody2;

  /// No description provided for @repairStreakSuccessDialogButton.
  ///
  /// In en, this message translates to:
  /// **'Back to Goals'**
  String get repairStreakSuccessDialogButton;

  /// No description provided for @startFreshConfirmationTitle.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to\nstart fresh?'**
  String get startFreshConfirmationTitle;

  /// No description provided for @startFreshConfirmationBody.
  ///
  /// In en, this message translates to:
  /// **'This will start your journey again from Week 1. You will not be able to recover your previous streak.'**
  String get startFreshConfirmationBody;

  /// No description provided for @startFreshConfirmationAccept.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get startFreshConfirmationAccept;

  /// No description provided for @startFreshConfirmationCancel.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get startFreshConfirmationCancel;

  /// No description provided for @saveYourStreaksTitle.
  ///
  /// In en, this message translates to:
  /// **'Save your streaks!'**
  String get saveYourStreaksTitle;

  /// No description provided for @saveYourStreaksSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t reach your weekly goal? No worries. Let\'s keep it going!'**
  String get saveYourStreaksSubtitle;

  /// No description provided for @saveYourStreaksTooltip.
  ///
  /// In en, this message translates to:
  /// **'Restore your streaks using NPs and continue your progress from where you left.'**
  String get saveYourStreaksTooltip;

  /// No description provided for @saveYourStreaksButton.
  ///
  /// In en, this message translates to:
  /// **'Back to Goals'**
  String get saveYourStreaksButton;

  /// No description provided for @menuButtonTooltip.
  ///
  /// In en, this message translates to:
  /// **'Open Menu'**
  String get menuButtonTooltip;

  /// No description provided for @menuTitle.
  ///
  /// In en, this message translates to:
  /// **'NEURO World'**
  String get menuTitle;

  /// No description provided for @menuItemProfile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get menuItemProfile;

  /// No description provided for @menuItemSubscription.
  ///
  /// In en, this message translates to:
  /// **'Subscription Plan'**
  String get menuItemSubscription;

  /// No description provided for @menuItemFeedback.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get menuItemFeedback;

  /// No description provided for @menuItemDeleteAccount.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get menuItemDeleteAccount;

  /// No description provided for @menuItemLogout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get menuItemLogout;

  /// No description provided for @streaksAchievementLabel.
  ///
  /// In en, this message translates to:
  /// **'Active Streaks'**
  String get streaksAchievementLabel;

  /// No description provided for @badgesAchievementLabel.
  ///
  /// In en, this message translates to:
  /// **'Total Badges'**
  String get badgesAchievementLabel;

  /// No description provided for @pointsAchievementLabel.
  ///
  /// In en, this message translates to:
  /// **'NEURO Points'**
  String get pointsAchievementLabel;

  /// No description provided for @homeTabCardAction.
  ///
  /// In en, this message translates to:
  /// **'Tap to view'**
  String get homeTabCardAction;

  /// No description provided for @todaysGoalProgressCardLabel.
  ///
  /// In en, this message translates to:
  /// **'Goals Progress'**
  String get todaysGoalProgressCardLabel;

  /// No description provided for @todaysGoalProgressCardIncompleteLabel.
  ///
  /// In en, this message translates to:
  /// **'Don\'t forget to check-in on your goals.'**
  String get todaysGoalProgressCardIncompleteLabel;

  /// No description provided for @todaysGoalProgressCardCompleteLabel.
  ///
  /// In en, this message translates to:
  /// **'Nice work! All goals are completed for today.'**
  String get todaysGoalProgressCardCompleteLabel;

  /// No description provided for @restoreStreakCardLastDaySingle.
  ///
  /// In en, this message translates to:
  /// **'You\'re close to breaking a streak. Check-in to save it!'**
  String get restoreStreakCardLastDaySingle;

  /// No description provided for @restoreStreakCardLastDayMultiple.
  ///
  /// In en, this message translates to:
  /// **'goals are at risk of losing their streaks. Tap to act now!'**
  String get restoreStreakCardLastDayMultiple;

  /// No description provided for @restoreStreakCardBrokenStreaks1.
  ///
  /// In en, this message translates to:
  /// **'Only'**
  String get restoreStreakCardBrokenStreaks1;

  /// No description provided for @restoreStreakCardBrokenStreaks2Single.
  ///
  /// In en, this message translates to:
  /// **'day left to repair your broken streak! Tap now before it\'s lost.'**
  String get restoreStreakCardBrokenStreaks2Single;

  /// No description provided for @restoreStreakCardBrokenStreaks2Multiple.
  ///
  /// In en, this message translates to:
  /// **'days left to repair your broken streaks! Tap now before it\'s lost.'**
  String get restoreStreakCardBrokenStreaks2Multiple;

  /// No description provided for @neuroCityCardLabel.
  ///
  /// In en, this message translates to:
  /// **'NEURO City'**
  String get neuroCityCardLabel;

  /// No description provided for @neuroCityCardText.
  ///
  /// In en, this message translates to:
  /// **'Your Neuropolis city is evolving. Check it out! 🎉'**
  String get neuroCityCardText;

  /// No description provided for @currentPlanCardLabel.
  ///
  /// In en, this message translates to:
  /// **'Current Plan'**
  String get currentPlanCardLabel;

  /// No description provided for @currentPlanCardText.
  ///
  /// In en, this message translates to:
  /// **'Unlock Premium: Get personalized insights and exclusive features—upgrade now for the ultimate experience!'**
  String get currentPlanCardText;

  /// No description provided for @goalDetailMylaTooltip.
  ///
  /// In en, this message translates to:
  /// **'Here is everything you need to know about'**
  String get goalDetailMylaTooltip;

  /// No description provided for @goalsMoreEditOption.
  ///
  /// In en, this message translates to:
  /// **'Edit Goal'**
  String get goalsMoreEditOption;

  /// No description provided for @goalsMoreDetailsOption.
  ///
  /// In en, this message translates to:
  /// **'Goal Details'**
  String get goalsMoreDetailsOption;

  /// No description provided for @editGoalSheetTitle.
  ///
  /// In en, this message translates to:
  /// **'Edit Goal'**
  String get editGoalSheetTitle;

  /// No description provided for @editGoalSheetSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Change the frequency of your current goal to the desired level.'**
  String get editGoalSheetSubtitle;

  /// No description provided for @nutritionGoal.
  ///
  /// In en, this message translates to:
  /// **'Set your Nutrition goal'**
  String get nutritionGoal;

  /// No description provided for @onboardingStepOne.
  ///
  /// In en, this message translates to:
  /// **'My strong and steady friend who knows that real power starts with the right fuel. He\'s mastered the art of brain-healthy eating and will help you nourish your mind with simple, delicious choices!'**
  String get onboardingStepOne;

  /// No description provided for @meetNuri.
  ///
  /// In en, this message translates to:
  /// **'Let\'s meet Nuri'**
  String get meetNuri;

  /// No description provided for @nutritionChoice.
  ///
  /// In en, this message translates to:
  /// **'Hi, I\'m Nuri! I\'ll be your guide to making better nutrition choices.'**
  String get nutritionChoice;

  /// No description provided for @strongMind.
  ///
  /// In en, this message translates to:
  /// **'Strong minds need strong food. Pick a few delicious habits!'**
  String get strongMind;

  /// No description provided for @letsGo.
  ///
  /// In en, this message translates to:
  /// **'Sure, Lets go!'**
  String get letsGo;

  /// No description provided for @changeGoal.
  ///
  /// In en, this message translates to:
  /// **'Change Goal'**
  String get changeGoal;

  /// No description provided for @changeIsland.
  ///
  /// In en, this message translates to:
  /// **'Change Island'**
  String get changeIsland;

  /// No description provided for @changeIntensity.
  ///
  /// In en, this message translates to:
  /// **'Change Intensity'**
  String get changeIntensity;

  /// No description provided for @setIntensity.
  ///
  /// In en, this message translates to:
  /// **'Lets set the intensity of your goal.'**
  String get setIntensity;

  /// No description provided for @confirmDays.
  ///
  /// In en, this message translates to:
  /// **'Confirm Days'**
  String get confirmDays;

  /// No description provided for @greatJob.
  ///
  /// In en, this message translates to:
  /// **'Great job! we are almost there. now lets set the days for this goal and are good to go.'**
  String get greatJob;

  /// No description provided for @congratulationsUnlock.
  ///
  /// In en, this message translates to:
  /// **'Congratulations! You have unlocked a new land by setting up this goal.'**
  String get congratulationsUnlock;

  /// No description provided for @explore.
  ///
  /// In en, this message translates to:
  /// **'Now let\'s explore the vibrant land you\'ve just unlocked.'**
  String get explore;

  /// No description provided for @exploreCity.
  ///
  /// In en, this message translates to:
  /// **'Explore the city'**
  String get exploreCity;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @gettingGoal.
  ///
  /// In en, this message translates to:
  /// **'Getting your goals'**
  String get gettingGoal;

  /// No description provided for @settingGoal.
  ///
  /// In en, this message translates to:
  /// **'Setting your goal'**
  String get settingGoal;

  /// No description provided for @selectIsland.
  ///
  /// In en, this message translates to:
  /// **'Select island'**
  String get selectIsland;

  /// No description provided for @askMe.
  ///
  /// In en, this message translates to:
  /// **'Ask me anything'**
  String get askMe;

  /// No description provided for @letMeKnow.
  ///
  /// In en, this message translates to:
  /// **'Let me know if you need help picking up a new habit'**
  String get letMeKnow;

  /// No description provided for @emptyGoalsTitle.
  ///
  /// In en, this message translates to:
  /// **'No Goals Available'**
  String get emptyGoalsTitle;

  /// No description provided for @emptyGoalsText.
  ///
  /// In en, this message translates to:
  /// **'Oops! You have not selected any goals. \nTap the button below to get started.'**
  String get emptyGoalsText;

  /// No description provided for @noTrackingFound.
  ///
  /// In en, this message translates to:
  /// **'Oops! You didn\'t track any goals on this date. \nSelect a future date to see your goals.'**
  String get noTrackingFound;

  /// No description provided for @emptyGoalsAction.
  ///
  /// In en, this message translates to:
  /// **'Add Goal'**
  String get emptyGoalsAction;

  /// No description provided for @goalsDescriptionEmpty.
  ///
  /// In en, this message translates to:
  /// **'This goal does not have a description yet.'**
  String get goalsDescriptionEmpty;

  /// No description provided for @editGoalLoading.
  ///
  /// In en, this message translates to:
  /// **'Updating goal...'**
  String get editGoalLoading;

  /// No description provided for @awardDaily.
  ///
  /// In en, this message translates to:
  /// **'Daily Check-in'**
  String get awardDaily;

  /// No description provided for @awardExtra.
  ///
  /// In en, this message translates to:
  /// **'Extra Check-in'**
  String get awardExtra;

  /// No description provided for @awardWeekStreak.
  ///
  /// In en, this message translates to:
  /// **'Week Streak Achieved'**
  String get awardWeekStreak;

  /// No description provided for @awardMonthStreak.
  ///
  /// In en, this message translates to:
  /// **'Month Streak Achieved'**
  String get awardMonthStreak;

  /// No description provided for @award3MonthStreak.
  ///
  /// In en, this message translates to:
  /// **'3 Months Streak Achieved'**
  String get award3MonthStreak;

  /// No description provided for @award6MonthStreak.
  ///
  /// In en, this message translates to:
  /// **'6 Months Streak Achieved'**
  String get award6MonthStreak;

  /// No description provided for @goalCardUnstarted.
  ///
  /// In en, this message translates to:
  /// **'Check-in to start your goal.'**
  String get goalCardUnstarted;

  /// No description provided for @goalCardFinalChance.
  ///
  /// In en, this message translates to:
  /// **'Final chance to save your streak!'**
  String get goalCardFinalChance;

  /// No description provided for @goalCardNoCheckin.
  ///
  /// In en, this message translates to:
  /// **'Check-in now and stay on track.'**
  String get goalCardNoCheckin;

  /// No description provided for @goalCardOneCheckin.
  ///
  /// In en, this message translates to:
  /// **'Great start! Keep going.'**
  String get goalCardOneCheckin;

  /// No description provided for @goalCardOneAway.
  ///
  /// In en, this message translates to:
  /// **'You\'re just one check-in away!'**
  String get goalCardOneAway;

  /// No description provided for @goalCardTwoAway.
  ///
  /// In en, this message translates to:
  /// **'You\'re just two check-ins away!'**
  String get goalCardTwoAway;

  /// No description provided for @goalCardHalfWay.
  ///
  /// In en, this message translates to:
  /// **'You\'re halfway there! Keep it up.'**
  String get goalCardHalfWay;

  /// No description provided for @goalCardDone.
  ///
  /// In en, this message translates to:
  /// **'Weekly goal done!'**
  String get goalCardDone;

  /// No description provided for @goalCardExtra.
  ///
  /// In en, this message translates to:
  /// **'Extra check-ins, going beyond!'**
  String get goalCardExtra;

  /// No description provided for @goalCardDefault.
  ///
  /// In en, this message translates to:
  /// **'Meet your goal.'**
  String get goalCardDefault;

  /// No description provided for @comingSoon.
  ///
  /// In en, this message translates to:
  /// **'Coming Soon!'**
  String get comingSoon;

  /// No description provided for @nutritionTitle.
  ///
  /// In en, this message translates to:
  /// **'Nutrition'**
  String get nutritionTitle;

  /// No description provided for @nutritionSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Brain-healthy diets and cooking.'**
  String get nutritionSubtitle;

  /// No description provided for @exerciseTitle.
  ///
  /// In en, this message translates to:
  /// **'Exercise'**
  String get exerciseTitle;

  /// No description provided for @exerciseSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Workouts, sports, and movement.'**
  String get exerciseSubtitle;

  /// No description provided for @unwindTitle.
  ///
  /// In en, this message translates to:
  /// **'Unwind'**
  String get unwindTitle;

  /// No description provided for @unwindSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Stress management and meditation.'**
  String get unwindSubtitle;

  /// No description provided for @restoreTitle.
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get restoreTitle;

  /// No description provided for @restoreSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Consistent good-quality sleep.'**
  String get restoreSubtitle;

  /// No description provided for @optimizeTitle.
  ///
  /// In en, this message translates to:
  /// **'Optimize'**
  String get optimizeTitle;

  /// No description provided for @optimizeSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Activities, hobbies, and socialising.'**
  String get optimizeSubtitle;

  /// No description provided for @listView.
  ///
  /// In en, this message translates to:
  /// **'List View'**
  String get listView;

  /// No description provided for @mapView.
  ///
  /// In en, this message translates to:
  /// **'Map View'**
  String get mapView;

  /// No description provided for @profileScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileScreenTitle;

  /// No description provided for @profileEmail.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get profileEmail;

  /// No description provided for @profileName.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get profileName;

  /// No description provided for @profileDoB.
  ///
  /// In en, this message translates to:
  /// **'Date of Birth'**
  String get profileDoB;

  /// No description provided for @profileDoBField.
  ///
  /// In en, this message translates to:
  /// **'Select Date of Birth'**
  String get profileDoBField;

  /// No description provided for @profileGender.
  ///
  /// In en, this message translates to:
  /// **'Gender'**
  String get profileGender;

  /// No description provided for @profileGenderField.
  ///
  /// In en, this message translates to:
  /// **'Select Gender'**
  String get profileGenderField;

  /// No description provided for @profileNameHint.
  ///
  /// In en, this message translates to:
  /// **'Enter your name'**
  String get profileNameHint;

  /// No description provided for @profileDoBHint.
  ///
  /// In en, this message translates to:
  /// **'Select a date'**
  String get profileDoBHint;

  /// No description provided for @profileGenderHint.
  ///
  /// In en, this message translates to:
  /// **'Select gender'**
  String get profileGenderHint;

  /// No description provided for @completeProfileCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Complete Your Profile'**
  String get completeProfileCardTitle;

  /// No description provided for @completeProfileCardBody.
  ///
  /// In en, this message translates to:
  /// **'Complete your profile to unlock a more tailored and personalized experience.'**
  String get completeProfileCardBody;

  /// No description provided for @completeProfileScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Complete Profile'**
  String get completeProfileScreenTitle;

  /// No description provided for @completeProfileScreenButton.
  ///
  /// In en, this message translates to:
  /// **'Complete Profile'**
  String get completeProfileScreenButton;

  /// No description provided for @editProfileScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfileScreenTitle;

  /// No description provided for @editProfileScreenButton.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get editProfileScreenButton;

  /// No description provided for @editProfileScreenAddPicture.
  ///
  /// In en, this message translates to:
  /// **'Add Picture'**
  String get editProfileScreenAddPicture;

  /// No description provided for @editProfileScreenUpdatePicture.
  ///
  /// In en, this message translates to:
  /// **'Update Picture'**
  String get editProfileScreenUpdatePicture;

  /// No description provided for @editProfilePictureCamera.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get editProfilePictureCamera;

  /// No description provided for @editProfilePictureGallery.
  ///
  /// In en, this message translates to:
  /// **'Choose from Gallery'**
  String get editProfilePictureGallery;

  /// No description provided for @updateProfilePicureLoadingText.
  ///
  /// In en, this message translates to:
  /// **'Updating Picture...'**
  String get updateProfilePicureLoadingText;

  /// No description provided for @updateProfileLoadingText.
  ///
  /// In en, this message translates to:
  /// **'Saving Changes...'**
  String get updateProfileLoadingText;

  /// No description provided for @onboardingStepOneTitle.
  ///
  /// In en, this message translates to:
  /// **'Welcome to\nNEURO World!'**
  String get onboardingStepOneTitle;

  /// No description provided for @onboardingStepTwoTitle.
  ///
  /// In en, this message translates to:
  /// **'Getting Started\nwith Myla'**
  String get onboardingStepTwoTitle;

  /// No description provided for @onboardingStepOneDescription.
  ///
  /// In en, this message translates to:
  /// **'Your journey to a healthier, sharper, and more resilient mind starts now. Myla, your guide, will support you every step of the way. Let\'s build habits that transform your brain and body!'**
  String get onboardingStepOneDescription;

  /// No description provided for @onboardingStepTwoDescription.
  ///
  /// In en, this message translates to:
  /// **'Hi there! I\'m Myla, the brain of NEURO World. I\'ll be your guide as you unlock islands of Nutrition, Exercise, Stress Management, Restorative Sleep, and Cognitive Optimization. Ready to begin? Let\'s go!'**
  String get onboardingStepTwoDescription;

  /// No description provided for @onboardingStepThreeDescription.
  ///
  /// In en, this message translates to:
  /// **'What kind of habit do you want to build? Tap the island we\'ll go to next!'**
  String get onboardingStepThreeDescription;

  /// No description provided for @stepOneButton.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get stepOneButton;

  /// No description provided for @letsGetStarted.
  ///
  /// In en, this message translates to:
  /// **'Let\'s Get Started'**
  String get letsGetStarted;

  /// No description provided for @confirmationDialogAcceptButton.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get confirmationDialogAcceptButton;

  /// No description provided for @confirmationDialogDeclineButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get confirmationDialogDeclineButton;

  /// No description provided for @feedbackScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedbackScreenTitle;

  /// No description provided for @feedbackScreenSubtitle.
  ///
  /// In en, this message translates to:
  /// **'How likely are you to recommend us to a friend or colleagues?'**
  String get feedbackScreenSubtitle;

  /// No description provided for @feedbackScreenSubtitle2.
  ///
  /// In en, this message translates to:
  /// **'How would you share your experience with us?'**
  String get feedbackScreenSubtitle2;

  /// No description provided for @feedbackButtonText.
  ///
  /// In en, this message translates to:
  /// **'Submit Feedback'**
  String get feedbackButtonText;

  /// No description provided for @feedbackShareExperience.
  ///
  /// In en, this message translates to:
  /// **'Share your experience here...'**
  String get feedbackShareExperience;

  /// No description provided for @submittingFeedbackLoadingText.
  ///
  /// In en, this message translates to:
  /// **'Submitting Feedback...'**
  String get submittingFeedbackLoadingText;

  /// No description provided for @nameLengthValidation.
  ///
  /// In en, this message translates to:
  /// **'Name must be less than 30 characters'**
  String get nameLengthValidation;

  /// No description provided for @validationPasswordMaxLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be less than 50 characters'**
  String get validationPasswordMaxLength;

  /// No description provided for @validationEmailMaxLength.
  ///
  /// In en, this message translates to:
  /// **'Email must be less than 50 characters'**
  String get validationEmailMaxLength;

  /// No description provided for @feedbackSubtitle.
  ///
  /// In en, this message translates to:
  /// **'How likely are you to recommend us to a friend or colleague?'**
  String get feedbackSubtitle;

  /// No description provided for @notificationTitle.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notificationTitle;

  /// No description provided for @notificationNewLabel.
  ///
  /// In en, this message translates to:
  /// **'New'**
  String get notificationNewLabel;

  /// No description provided for @notificationEarlierLabel.
  ///
  /// In en, this message translates to:
  /// **'Earlier'**
  String get notificationEarlierLabel;

  /// No description provided for @notificationMarkRead.
  ///
  /// In en, this message translates to:
  /// **'Mark as all read'**
  String get notificationMarkRead;

  /// No description provided for @notificationPermissionDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Enable alerts'**
  String get notificationPermissionDialogTitle;

  /// No description provided for @notificationPermissionDialogDescription.
  ///
  /// In en, this message translates to:
  /// **'Enable alerts to keep track of your goals and streaks!'**
  String get notificationPermissionDialogDescription;

  /// No description provided for @notificationPermissionDialogAcept.
  ///
  /// In en, this message translates to:
  /// **'Yes, I\'m in!'**
  String get notificationPermissionDialogAcept;

  /// No description provided for @notificationPermissionDialogLater.
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get notificationPermissionDialogLater;

  /// No description provided for @deleteAccountScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccountScreenTitle;

  /// No description provided for @deleteAccountConfirmationTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete Account Confirmation'**
  String get deleteAccountConfirmationTitle;

  /// No description provided for @deleteAccountConfirmationSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to permanently delete your account? This action cannot be undone, and all your data and profile information will be lost.'**
  String get deleteAccountConfirmationSubtitle;

  /// No description provided for @deleteAccountButtonTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete my account'**
  String get deleteAccountButtonTitle;

  /// No description provided for @deleteAccountSuccessButton.
  ///
  /// In en, this message translates to:
  /// **'Okay'**
  String get deleteAccountSuccessButton;

  /// No description provided for @deleteAccountConfirmTitle.
  ///
  /// In en, this message translates to:
  /// **'Account Deletion Confirmation'**
  String get deleteAccountConfirmTitle;

  /// No description provided for @deleteAccountConfirmSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Your account has been successfully deleted. All your personal data has been securely removed from our systems. Thank you for using our services.'**
  String get deleteAccountConfirmSubtitle;

  /// No description provided for @deleteAccountLoading.
  ///
  /// In en, this message translates to:
  /// **'Deleting Account...'**
  String get deleteAccountLoading;

  /// No description provided for @signInPrefix.
  ///
  /// In en, this message translates to:
  /// **'By tapping Sign In or logging into an existing '**
  String get signInPrefix;

  /// No description provided for @signInMiddle.
  ///
  /// In en, this message translates to:
  /// **' account, you agree to our\n'**
  String get signInMiddle;

  /// No description provided for @termsLink.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get termsLink;

  /// No description provided for @signInBeforePrivacy.
  ///
  /// In en, this message translates to:
  /// **' and acknowledge that you have read our '**
  String get signInBeforePrivacy;

  /// No description provided for @privacyLink.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy.'**
  String get privacyLink;

  /// No description provided for @landingSignupScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Create new account'**
  String get landingSignupScreenTitle;

  /// No description provided for @landingSignupScreenSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Let\'s begin your brain health journey\nwith NEURO World!'**
  String get landingSignupScreenSubtitle;

  /// No description provided for @landingSignupSSOApple.
  ///
  /// In en, this message translates to:
  /// **'Continue with Apple'**
  String get landingSignupSSOApple;

  /// No description provided for @landingSignupSSOGoogle.
  ///
  /// In en, this message translates to:
  /// **'Continue with Google'**
  String get landingSignupSSOGoogle;

  /// No description provided for @landingSignupSSOEmail.
  ///
  /// In en, this message translates to:
  /// **'Continue with Email'**
  String get landingSignupSSOEmail;

  /// No description provided for @privacyPolicyTitle.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicyTitle;

  /// No description provided for @resetPasswordScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordScreenTitle;

  /// No description provided for @resetPasswordScreenSubTitle.
  ///
  /// In en, this message translates to:
  /// **'Let\'s reset and get you back on your journey!'**
  String get resetPasswordScreenSubTitle;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @newPasswordPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter New Password'**
  String get newPasswordPlaceholder;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @confirmPasswordPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Enter Confirm Password'**
  String get confirmPasswordPlaceholder;

  /// No description provided for @subscriptionPlans.
  ///
  /// In en, this message translates to:
  /// **'Subscription Plans'**
  String get subscriptionPlans;

  /// No description provided for @monthly.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// No description provided for @yearly.
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// No description provided for @freeTier.
  ///
  /// In en, this message translates to:
  /// **'Free Tier'**
  String get freeTier;

  /// No description provided for @habitsTier.
  ///
  /// In en, this message translates to:
  /// **'Habits Tier'**
  String get habitsTier;

  /// No description provided for @summitTier.
  ///
  /// In en, this message translates to:
  /// **'Summit Tier'**
  String get summitTier;

  /// No description provided for @clinicalTier.
  ///
  /// In en, this message translates to:
  /// **'Clinical Tier'**
  String get clinicalTier;

  /// No description provided for @subscriptionPlanTitle.
  ///
  /// In en, this message translates to:
  /// **'Subscription Plan'**
  String get subscriptionPlanTitle;

  /// No description provided for @subscriptionPlanFeatures.
  ///
  /// In en, this message translates to:
  /// **'Features'**
  String get subscriptionPlanFeatures;

  /// No description provided for @subscriptionPlanPrice.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get subscriptionPlanPrice;

  /// No description provided for @subscriptionPlanSelect.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get subscriptionPlanSelect;

  /// No description provided for @subscriptionPlanSelected.
  ///
  /// In en, this message translates to:
  /// **'Selected'**
  String get subscriptionPlanSelected;

  /// No description provided for @subscriptionPlanMonthly.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get subscriptionPlanMonthly;

  /// No description provided for @upgrade.
  ///
  /// In en, this message translates to:
  /// **'Upgrade'**
  String get upgrade;

  /// No description provided for @perMonth.
  ///
  /// In en, this message translates to:
  /// **'Per Month'**
  String get perMonth;

  /// No description provided for @perYear.
  ///
  /// In en, this message translates to:
  /// **'Per Year'**
  String get perYear;

  /// No description provided for @purchaseSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Purchase Successful!'**
  String get purchaseSuccessful;

  /// No description provided for @purchaseFailed.
  ///
  /// In en, this message translates to:
  /// **'Purchase Failed'**
  String get purchaseFailed;

  /// No description provided for @purchaseCancelled.
  ///
  /// In en, this message translates to:
  /// **'Purchase Cancelled'**
  String get purchaseCancelled;

  /// No description provided for @purchaseError.
  ///
  /// In en, this message translates to:
  /// **'Purchase Error'**
  String get purchaseError;

  /// No description provided for @purchaseErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'An error occurred during purchase. Please try again.'**
  String get purchaseErrorMessage;

  /// No description provided for @purchaseInitiationFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to initiate purchase. Please try again.'**
  String get purchaseInitiationFailed;

  /// No description provided for @productNotFound.
  ///
  /// In en, this message translates to:
  /// **'Product not found. Please try again.'**
  String get productNotFound;

  /// No description provided for @storeNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'Store not available'**
  String get storeNotAvailable;

  /// No description provided for @loadingProducts.
  ///
  /// In en, this message translates to:
  /// **'Loading products...'**
  String get loadingProducts;

  /// No description provided for @productsLoadFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to load products'**
  String get productsLoadFailed;

  /// No description provided for @freeTierSelected.
  ///
  /// In en, this message translates to:
  /// **'Free Tier Selected'**
  String get freeTierSelected;

  /// No description provided for @freeTierMessage.
  ///
  /// In en, this message translates to:
  /// **'You have selected the {tier} tier. Enjoy the free features!'**
  String freeTierMessage(Object tier);

  /// No description provided for @welcomeToTier.
  ///
  /// In en, this message translates to:
  /// **'Welcome to {tier}! Your subscription is now active.'**
  String welcomeToTier(Object tier);

  /// No description provided for @confirmPurchase.
  ///
  /// In en, this message translates to:
  /// **'Confirm Purchase'**
  String get confirmPurchase;

  /// No description provided for @purchaseConfirmationMessage.
  ///
  /// In en, this message translates to:
  /// **'You are about to purchase:'**
  String get purchaseConfirmationMessage;

  /// No description provided for @processingPurchase.
  ///
  /// In en, this message translates to:
  /// **'Processing purchase...'**
  String get processingPurchase;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @purchase.
  ///
  /// In en, this message translates to:
  /// **'Purchase'**
  String get purchase;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
