<!-- app colors pulled from figma -->
<colors>
    <color name="surface_primary">#FFFFFF</color>
    <color name="surface_secondary">#F8F9FA</color>
    <color name="bold_orange">#FF9317</color>
    <color name="bold_blue">#61D2E5</color>
    <color name="unwind_orange">#FFF4E8</color>
    <color name="unwind_blue">#EDF9FC</color>
    <color name="primary_button">#EDF9FC</color>
    <color name="text_primary">#16191D</color>
    <color name="text_secondary">#4B4B4B</color>
    <color name="text_tertiary">#8A96A8</color>
    <color name="disabled_text">#A7B1BE</color>
    <color name="stroke">#D3D8DE</color>
    <color name="green">#12A012</color>
    <color name="error">#F22E30</color>
    <color name="secondary_blue">#1994C5</color>
    <color name="secondary_orange">#FEBEA2</color>
    <color name="app_bar_orange_begin">#FD8755</color>
    <color name="app_bar_orange_end">#F68D41</color>
    <color name="drop_shadow">#10182808</color>
    <color name="bottom_sheet_barrier">#3A3A3A</color>
    <color name="bottom_sheet_handle">#CBD2D9</color>
    <color name="icon_background">#F4F4F4</color>
    <color name="primary_button_shadow">#DF7802</color>

    <!-- NEURO Colors-->
    <color name="nutrition">#00C800</color>
    <color name="exercise">#FFC005</color>
    <color name="unwind">#A377FF</color>
    <color name="restore">#0591FF</color>
    <color name="optimize">#F30ECE</color>

    <!-- inputs-->
    <color name="input_inactive_text">#8A96A8</color>
    <color name="input_text">#363635</color>

    <!-- gradient -->
    <color name="orange_gradient_light">#F1973D</color>
    <color name="orange_gradient_dark">#FD7D46</color>

    <!-- landing screen -->
    <color name="divider_text">#7A7A7A</color>

    <!-- signup screen -->
    <color name="checkbox_border">#AAAAAA</color>

    <!-- toasts -->
    <color name="toast_text">#2F3F53</color>
    <color name="toast_close_button">#979FA9</color>
    <color name="error_toast_bg">#FFF5F3</color>
    <color name="success_toast_bg">#F6FFF9</color>
    <color name="success_toast_border">#48C1B5</color>

    <!-- chat screen -->
    <color name="myla_fab_drop_shadow">#10182808</color>
    <color name="send_button_disabled">#D7D7D7</color>
 
    <!-- onboarding screen -->
    <color name="brain_text">#4E555F</color>
    <color name="brain_bg">#E8F5FF</color>
    <color name="button_disabled">#BCBDC5</color>

    <color name="chat_bubble">#E8F5FF</color>
    <color name="chat_bubble_text">#4E555F</color>
    
    <!-- goals tab-->
    <color name="goal_streak_outline">#EEEEEE</color>
    <color name="goal_streak_empty">#D5D5D5</color>
    <color name="goal_card_divider">#F4F4F4</color>
    <color name="slider_gradient_begin">#E7E7E7</color>
    <color name="slider_gradient_end">#F1F1F1F2</color>
    <color name="slider_button_drop_shadow">#00000030</color>
    <color name="slider_enable_gradient_begin">#9A73D75F</color>
    <color name="slider_enable_gradient_end">#00C800</color>
    <color name="slider_recently_tracked">#C3E3B8</color>
    <color name="restore_label">#FF7E82</color>

    <!-- menu drawer -->
    <color name="subscription_free">#3E9F5F</color>
    <color name="profile_begin_gradient">#3567DA</color>
    <color name="profile_end_gradient">#0DA1B1</color>

    <!-- neuro card-->
    <color name="restore_background">#0591FF</color>
    <color name="optimize_background">#F30ECE</color>
    <color name="nutrition_background">#00C800</color>
    <color name="exercise_background">#FFC005</color>
    <color name="unwind_background">#A377FF</color>

    <!-- feedback -->
    <color name="rating_row_one_background">#FBDFE6</color>
    <color name="rating_row_one">#E53C67</color>
    <color name="rating_row_two_left_background">#FCF5E1</color>
    <color name="rating_row_two_right_background">#DAFBEE</color>
    <color name="rating_row_two_left">#FAB801</color>
    <color name="rating_row_two_right">#3FB587</color>

    <!-- delete account-->
    <color name="delete_account_border">#DB0709</color>
    <color name="delete_icon_border">#FFDEDE</color>
    <color name="delete_icon_drop_shadow">#366B96</color>
    <color name="delete_account_success_icon_border">#CEE7E8</color>

    <!-- subscription-->
    <color name="habit_gradient_start">#F2F2F3</color>
    <color name="habit_gradient_end">#FFFFFF</color>

    <!-- subscription tier gradients -->
    <color name="free_gradient_start">#F2F2F3</color>
    <color name="free_gradient_end">#FFFFFF</color>
    <color name="habits_gradient_start">#E0EFF9</color>
    <color name="habits_gradient_end">#FFFFFF</color>
    <color name="summit_gradient_start">#D2E6E0</color>
    <color name="summit_gradient_end">#FFFFFF</color>
    <color name="clinical_gradient_start">#F8E8FF</color>
    <color name="clinical_gradient_end">#FCF0FF</color>


</colors>